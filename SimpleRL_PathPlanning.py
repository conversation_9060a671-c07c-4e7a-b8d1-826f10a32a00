import numpy as np
import matplotlib.pyplot as plt
import random
from collections import defaultdict

class SimpleQLearningPathPlanner:
    """
    简化的Q-Learning路径规划算法
    只考虑起点、终点和地图，不涉及风险评估和多目标优化
    """
    def __init__(self, start, goal, grid_map, learning_rate=0.1, discount_factor=0.9, 
                 epsilon=0.1, epsilon_decay=0.995, min_epsilon=0.01, max_episodes=1000):
        """
        初始化Q-Learning路径规划器
        
        Args:
            start: 起点坐标 (x, y)
            goal: 终点坐标 (x, y)
            grid_map: 网格地图，0表示自由空间，非0表示障碍物
            learning_rate: 学习率 α
            discount_factor: 折扣因子 γ
            epsilon: 探索率 ε
            epsilon_decay: 探索率衰减
            min_epsilon: 最小探索率
            max_episodes: 最大训练回合数
        """
        self.start = start
        self.goal = goal
        self.grid_map = np.array(grid_map)
        self.map_size = self.grid_map.shape
        
        # Q-Learning参数
        self.learning_rate = learning_rate
        self.discount_factor = discount_factor
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.min_epsilon = min_epsilon
        self.max_episodes = max_episodes
        
        # 动作空间：8个方向
        self.actions = [(0, 1), (1, 0), (0, -1), (-1, 0),
                       (1, 1), (-1, 1), (-1, -1), (1, -1)]
        self.num_actions = len(self.actions)
        
        # Q表：Q(state, action)
        self.q_table = defaultdict(lambda: np.zeros(self.num_actions))
        
        # 训练统计
        self.episode_rewards = []
        self.episode_lengths = []
        self.success_episodes = []
        
    def _is_valid_state(self, state):
        """检查状态是否有效"""
        x, y = state
        return (0 <= x < self.map_size[0] and 
                0 <= y < self.map_size[1] and 
                self.grid_map[x, y] == 0)
    
    def _get_next_state(self, state, action_idx):
        """根据动作获取下一个状态"""
        x, y = state
        dx, dy = self.actions[action_idx]
        next_state = (x + dx, y + dy)
        
        if self._is_valid_state(next_state):
            return next_state
        else:
            return state  # 无效动作，保持原状态
    
    def _get_reward(self, state, action_idx, next_state):
        """计算简单的奖励函数"""
        # 到达目标的奖励
        if next_state == self.goal:
            return 100.0
        
        # 撞墙或越界的惩罚
        if not self._is_valid_state(next_state) or next_state == state:
            return -10.0
        
        # 移动的基本代价
        dx, dy = self.actions[action_idx]
        if abs(dx) + abs(dy) == 2:  # 对角线移动
            step_cost = -1.4
        else:  # 直线移动
            step_cost = -1.0
        
        # 距离目标的启发式奖励
        current_dist = abs(state[0] - self.goal[0]) + abs(state[1] - self.goal[1])
        next_dist = abs(next_state[0] - self.goal[0]) + abs(next_state[1] - self.goal[1])
        distance_reward = (current_dist - next_dist) * 0.1
        
        return step_cost + distance_reward
    
    def _choose_action(self, state, training=True):
        """选择动作（ε-贪婪策略）"""
        if training and random.random() < self.epsilon:
            # 探索：随机选择动作
            return random.randint(0, self.num_actions - 1)
        else:
            # 利用：选择Q值最大的动作
            return np.argmax(self.q_table[state])
    
    def _update_q_table(self, state, action, reward, next_state, done):
        """更新Q表"""
        current_q = self.q_table[state][action]
        
        if done:
            # 终止状态，没有未来奖励
            target_q = reward
        else:
            # 使用Bellman方程更新
            max_next_q = np.max(self.q_table[next_state])
            target_q = reward + self.discount_factor * max_next_q
        
        # Q-learning更新规则
        self.q_table[state][action] = current_q + self.learning_rate * (target_q - current_q)
    
    def train(self):
        """训练Q-learning智能体"""
        print("开始Q-Learning训练...")
        
        for episode in range(self.max_episodes):
            state = self.start
            total_reward = 0
            steps = 0
            max_steps = self.map_size[0] * self.map_size[1] * 2  # 防止无限循环
            
            while state != self.goal and steps < max_steps:
                # 选择动作
                action = self._choose_action(state, training=True)
                
                # 执行动作
                next_state = self._get_next_state(state, action)
                reward = self._get_reward(state, action, next_state)
                done = (next_state == self.goal)
                
                # 更新Q表
                self._update_q_table(state, action, reward, next_state, done)
                
                # 更新状态和统计
                state = next_state
                total_reward += reward
                steps += 1
            
            # 记录训练统计
            self.episode_rewards.append(total_reward)
            self.episode_lengths.append(steps)
            self.success_episodes.append(state == self.goal)
            
            # 衰减探索率
            if self.epsilon > self.min_epsilon:
                self.epsilon *= self.epsilon_decay
            
            # 打印进度
            if episode % 100 == 0:
                recent_success_rate = np.mean(self.success_episodes[-100:]) if len(self.success_episodes) >= 100 else np.mean(self.success_episodes)
                print(f"Episode {episode}: Success Rate = {recent_success_rate:.2f}, Epsilon = {self.epsilon:.3f}")
        
        print("训练完成!")
    
    def plan_path(self):
        """使用训练好的策略规划路径"""
        path = []
        state = self.start
        max_steps = self.map_size[0] * self.map_size[1]
        
        while state != self.goal and len(path) < max_steps:
            path.append(state)
            action = self._choose_action(state, training=False)  # 不探索，只利用
            state = self._get_next_state(state, action)
            
            # 防止无限循环
            if state in path:
                break
        
        if state == self.goal:
            path.append(state)
            return path
        else:
            return None
    
    def visualize_training(self):
        """可视化训练过程"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 奖励曲线
        axes[0, 0].plot(self.episode_rewards)
        axes[0, 0].set_title('Episode Rewards')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Total Reward')
        
        # 步数曲线
        axes[0, 1].plot(self.episode_lengths)
        axes[0, 1].set_title('Episode Lengths')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Steps')
        
        # 成功率曲线
        window_size = 100
        if len(self.success_episodes) >= window_size:
            success_rate = [np.mean(self.success_episodes[i:i+window_size]) 
                           for i in range(len(self.success_episodes) - window_size + 1)]
            axes[1, 0].plot(success_rate)
        axes[1, 0].set_title(f'Success Rate (Window={window_size})')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Success Rate')
        
        # Q值热图
        q_values = np.zeros(self.map_size)
        for (x, y), q_vals in self.q_table.items():
            if 0 <= x < self.map_size[0] and 0 <= y < self.map_size[1]:
                q_values[x, y] = np.max(q_vals)
        
        im = axes[1, 1].imshow(q_values.T, cmap='viridis', origin='lower')
        axes[1, 1].set_title('Max Q-Values')
        plt.colorbar(im, ax=axes[1, 1])
        
        plt.tight_layout()
        plt.show()
    
    def visualize_path(self, path=None):
        """可视化路径"""
        if path is None:
            path = self.plan_path()
        
        plt.figure(figsize=(8, 8))
        
        # 绘制地图
        plt.imshow(self.grid_map, cmap='gray_r', alpha=0.7)
        
        # 绘制Q值作为背景
        q_values = np.zeros(self.map_size)
        for (x, y), q_vals in self.q_table.items():
            if 0 <= x < self.map_size[0] and 0 <= y < self.map_size[1]:
                q_values[x, y] = np.max(q_vals)
        
        plt.imshow(q_values.T, cmap='Blues', alpha=0.3, origin='lower')
        
        # 绘制路径
        if path:
            path_array = np.array(path)
            plt.plot(path_array[:, 0], path_array[:, 1], 'g-', linewidth=2, label='RL Path')
            plt.plot(path_array[:, 0], path_array[:, 1], 'go', markersize=4)
            print(f"找到路径，长度: {len(path)} 步")
        else:
            print("未找到路径")
            # 显示学习到的Q值分布
            plt.title('Q-Learning 探索结果（未找到路径）')
        
        # 绘制起点和终点
        plt.plot(self.start[0], self.start[1], 'go', markersize=10, label='Start')
        plt.plot(self.goal[0], self.goal[1], 'ro', markersize=10, label='Goal')
        
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.title('Q-Learning Path Planning')
        plt.axis('equal')
        plt.show()
        
        return path

def main():
    """测试简化Q-Learning路径规划"""
    # 创建测试地图
    grid_map = np.array([
        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
        [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
        [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
        [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
        [0, 0, 1, 0, 0, 0, 0, 1, 0, 0],
        [0, 0, 1, 0, 0, 0, 0, 1, 0, 0],
        [0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
        [0, 1, 1, 0, 0, 1, 0, 1, 1, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    start = (0, 0)
    goal = (9, 9)
    
    # 创建简化Q-Learning规划器
    planner = SimpleQLearningPathPlanner(start, goal, grid_map, max_episodes=2000)
    
    # 训练
    planner.train()
    
    # 可视化训练过程
    planner.visualize_training()
    
    # 规划路径并可视化
    planner.visualize_path()

if __name__ == "__main__":
    main()
