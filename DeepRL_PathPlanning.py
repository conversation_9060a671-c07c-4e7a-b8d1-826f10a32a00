"""
经典深度Q网络(DQN)路径规划实现

基于Nature DQN论文的标准实现：
"Human-level control through deep reinforcement learning" (<PERSON><PERSON><PERSON> et al., 2015)

核心特性：
1. 经验回放 (Experience Replay)
2. 目标网络 (Target Network)
3. 卷积神经网络架构
4. ε-贪婪探索策略
5. <PERSON><PERSON>损失函数
6. RMSprop优化器

算法流程：
1. 初始化主网络Q和目标网络Q'
2. 初始化经验回放缓冲区D
3. 对每个回合：
   a. 观察状态s
   b. 用ε-贪婪策略选择动作a
   c. 执行动作，观察奖励r和下一状态s'
   d. 存储经验(s,a,r,s')到D
   e. 从D中采样批次进行训练
   f. 定期更新目标网络Q' ← Q
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import random
import matplotlib.pyplot as plt
from collections import deque

class DQNNetwork(nn.Module):
    """经典DQN网络 - 基于Nature DQN论文的标准实现"""
    def __init__(self, input_channels=3, action_size=8):
        super(DQNNetwork, self).__init__()

        # 经典DQN卷积层架构 (Nature DQN)
        # 第一层: 32个8x8卷积核，步长4
        self.conv1 = nn.Conv2d(input_channels, 32, kernel_size=8, stride=4, padding=0)
        # 第二层: 64个4x4卷积核，步长2
        self.conv2 = nn.Conv2d(32, 64, kernel_size=4, stride=2, padding=0)
        # 第三层: 64个3x3卷积核，步长1
        self.conv3 = nn.Conv2d(64, 64, kernel_size=3, stride=1, padding=0)

        # 计算卷积后的特征图大小
        self.feature_size = self._get_conv_output_size()

        # 经典DQN全连接层
        self.fc1 = nn.Linear(self.feature_size, 512)  # 标准512隐藏单元
        self.fc2 = nn.Linear(512, action_size)        # 输出Q值

        # 权重初始化 (Xavier初始化)
        self._initialize_weights()

    def _get_conv_output_size(self):
        """计算卷积层输出大小"""
        # 标准84x84输入
        x = torch.zeros(1, 3, 84, 84)
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))
        return x.view(1, -1).size(1)

    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        """前向传播"""
        # 卷积层 + ReLU激活
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))

        # 展平
        x = x.view(x.size(0), -1)

        # 全连接层
        x = F.relu(self.fc1(x))
        x = self.fc2(x)  # 输出Q值，不使用激活函数

        return x

class DuelingDQN(nn.Module):
    """Dueling DQN网络架构"""
    def __init__(self, input_channels=3, action_size=8, hidden_size=512):
        super(DuelingDQN, self).__init__()
        
        # 共享卷积层
        self.conv1 = nn.Conv2d(input_channels, 32, kernel_size=8, stride=4)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=4, stride=2)
        self.conv3 = nn.Conv2d(64, 64, kernel_size=3, stride=1)
        
        self.feature_size = self._get_conv_output_size()
        
        # 价值流 (Value Stream)
        self.value_fc1 = nn.Linear(self.feature_size, hidden_size)
        self.value_fc2 = nn.Linear(hidden_size, 1)
        
        # 优势流 (Advantage Stream)
        self.advantage_fc1 = nn.Linear(self.feature_size, hidden_size)
        self.advantage_fc2 = nn.Linear(hidden_size, action_size)
        
        self.dropout = nn.Dropout(0.3)
        
    def _get_conv_output_size(self):
        x = torch.zeros(1, 3, 84, 84)
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))
        return x.view(1, -1).size(1)
    
    def forward(self, x):
        # 共享特征提取
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))
        x = x.view(x.size(0), -1)
        
        # 价值流
        value = F.relu(self.value_fc1(x))
        value = self.dropout(value)
        value = self.value_fc2(value)
        
        # 优势流
        advantage = F.relu(self.advantage_fc1(x))
        advantage = self.dropout(advantage)
        advantage = self.advantage_fc2(advantage)
        
        # Dueling架构：Q(s,a) = V(s) + A(s,a) - mean(A(s,a))
        q_values = value + advantage - advantage.mean(dim=1, keepdim=True)
        return q_values

class ReplayBuffer:
    """经典DQN经验回放缓冲区"""
    def __init__(self, capacity=10000):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)

    def push(self, state, action, reward, next_state, done):
        """添加经验到缓冲区"""
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size):
        """随机采样一批经验"""
        batch = random.sample(self.buffer, batch_size)

        # 分离各个组件
        states = torch.FloatTensor([e[0] for e in batch])
        actions = torch.LongTensor([e[1] for e in batch])
        rewards = torch.FloatTensor([e[2] for e in batch])
        next_states = torch.FloatTensor([e[3] for e in batch])
        dones = torch.BoolTensor([e[4] for e in batch])

        return states, actions, rewards, next_states, dones

    def __len__(self):
        return len(self.buffer)

class ClassicDQN:
    """经典DQN算法实现 - 基于Nature DQN论文"""
    def __init__(self, start, goal, grid_map,
                 learning_rate=0.00025,  # Nature DQN标准学习率
                 gamma=0.99,
                 epsilon_start=1.0,
                 epsilon_end=0.1,        # Nature DQN标准最小epsilon
                 epsilon_decay_steps=1000000,  # 线性衰减步数
                 memory_size=1000000,    # Nature DQN标准缓冲区大小
                 batch_size=32,
                 target_update=10000,    # Nature DQN标准更新频率
                 train_start=50000):     # 开始训练前的预填充步数

        self.start = start
        self.goal = goal
        self.grid_map = np.array(grid_map)
        self.map_shape = self.grid_map.shape

        # DQN超参数 (Nature DQN标准)
        self.learning_rate = learning_rate
        self.gamma = gamma
        self.epsilon = epsilon_start
        self.epsilon_start = epsilon_start
        self.epsilon_end = epsilon_end
        self.epsilon_decay_steps = epsilon_decay_steps
        self.batch_size = batch_size
        self.target_update = target_update
        self.train_start = train_start

        # 动作空间：8个方向移动
        self.actions = [(-1,-1), (-1,0), (-1,1), (0,-1), (0,1), (1,-1), (1,0), (1,1)]
        self.action_size = len(self.actions)

        # 设备选择
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"经典DQN使用设备: {self.device}")

        # 网络初始化 (使用标准DQN网络)
        self.q_network = DQNNetwork(input_channels=3, action_size=self.action_size).to(self.device)
        self.target_network = DQNNetwork(input_channels=3, action_size=self.action_size).to(self.device)

        # 优化器 (RMSprop是Nature DQN的标准选择)
        self.optimizer = optim.RMSprop(self.q_network.parameters(),
                                     lr=learning_rate,
                                     alpha=0.95,
                                     eps=0.01)

        # 经验回放缓冲区
        self.memory = ReplayBuffer(memory_size)

        # 训练统计
        self.episode_rewards = []
        self.episode_lengths = []
        self.losses = []
        self.step_count = 0
        self.update_count = 0

        # 初始化目标网络
        self.update_target_network()
        
    def update_target_network(self):
        """更新目标网络"""
        self.target_network.load_state_dict(self.q_network.state_dict())
        
    def preprocess_state(self, position):
        """预处理状态，将环境信息转换为网络输入"""
        # 创建以当前位置为中心的局部观察窗口
        window_size = 84
        half_window = window_size // 2
        
        # 创建三通道状态表示
        state = np.zeros((3, window_size, window_size), dtype=np.float32)
        
        # 计算窗口在全局地图中的位置
        start_x = max(0, position[0] - half_window)
        end_x = min(self.map_shape[0], position[0] + half_window)
        start_y = max(0, position[1] - half_window)
        end_y = min(self.map_shape[1], position[1] + half_window)
        
        # 计算在状态数组中的位置
        state_start_x = half_window - (position[0] - start_x)
        state_end_x = state_start_x + (end_x - start_x)
        state_start_y = half_window - (position[1] - start_y)
        state_end_y = state_start_y + (end_y - start_y)
        
        # 通道0：障碍物信息
        state[0, state_start_x:state_end_x, state_start_y:state_end_y] = \
            self.grid_map[start_x:end_x, start_y:end_y]
            
        # 通道1：目标位置信息
        goal_x = self.goal[0] - position[0] + half_window
        goal_y = self.goal[1] - position[1] + half_window
        if 0 <= goal_x < window_size and 0 <= goal_y < window_size:
            state[1, goal_x, goal_y] = 1.0
            
        # 通道2：当前位置信息
        state[2, half_window, half_window] = 1.0
        
        return state
        
    def get_action(self, state, training=True):
        """epsilon-greedy动作选择策略"""
        if training and random.random() < self.epsilon:
            return random.randint(0, self.action_size - 1)

        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            q_values = self.q_network(state_tensor)
            return q_values.argmax().item()

    def update_epsilon(self):
        """线性衰减epsilon (Nature DQN标准)"""
        if self.step_count < self.epsilon_decay_steps:
            self.epsilon = self.epsilon_start - (self.epsilon_start - self.epsilon_end) * \
                          (self.step_count / self.epsilon_decay_steps)
        else:
            self.epsilon = self.epsilon_end
            
    def is_valid_position(self, pos):
        """检查位置是否有效"""
        x, y = pos
        if x < 0 or x >= self.map_shape[0] or y < 0 or y >= self.map_shape[1]:
            return False
        return self.grid_map[x, y] == 0  # 0表示可通行
        
    def get_reward(self, current_pos, next_pos, done):
        """计算奖励函数"""
        # 基础移动惩罚
        reward = -0.01
        
        # 到达目标的奖励
        if done and next_pos == self.goal:
            reward += 100.0
            
        # 撞墙惩罚
        if not self.is_valid_position(next_pos):
            reward -= 10.0
            
        # 距离奖励（鼓励向目标移动）
        current_dist = np.linalg.norm(np.array(current_pos) - np.array(self.goal))
        next_dist = np.linalg.norm(np.array(next_pos) - np.array(self.goal))
        reward += (current_dist - next_dist) * 0.1
        
        return reward

    def step(self, current_pos, action):
        """执行动作并返回下一个状态"""
        dx, dy = self.actions[action]
        next_pos = (current_pos[0] + dx, current_pos[1] + dy)

        # 检查是否到达目标
        done = (next_pos == self.goal)

        # 如果位置无效，保持在当前位置
        if not self.is_valid_position(next_pos):
            next_pos = current_pos

        reward = self.get_reward(current_pos, next_pos, done)

        return next_pos, reward, done

    def store_experience(self, state, action, reward, next_state, done):
        """存储经验到回放缓冲区"""
        self.memory.push(state, action, reward, next_state, done)

    def replay(self):
        """经验回放训练 (经典DQN)"""
        # 检查是否有足够的经验进行训练
        if len(self.memory) < self.train_start:
            return

        if len(self.memory) < self.batch_size:
            return

        # 从经验缓冲区采样
        states, actions, rewards, next_states, dones = self.memory.sample(self.batch_size)

        # 移动到设备
        states = states.to(self.device)
        actions = actions.to(self.device)
        rewards = rewards.to(self.device)
        next_states = next_states.to(self.device)
        dones = dones.to(self.device)

        # 计算当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))

        # 计算目标Q值 (使用目标网络)
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0].detach()
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)

        # 计算Huber损失 (Nature DQN使用)
        loss = F.smooth_l1_loss(current_q_values.squeeze(), target_q_values)

        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()

        # 梯度裁剪 (Nature DQN标准)
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 10.0)
        self.optimizer.step()

        # 记录损失
        self.losses.append(loss.item())

        # 更新目标网络
        if self.step_count % self.target_update == 0:
            self.update_target_network()
            print(f"目标网络已更新 (步数: {self.step_count})")

        # 更新epsilon
        self.update_epsilon()

    def train(self, max_episodes=2000, max_steps_per_episode=1000):
        """训练经典DQN智能体"""
        print(f"开始训练经典DQN智能体")
        print(f"最大回合数: {max_episodes}")
        print(f"预填充步数: {self.train_start}")
        print(f"目标网络更新频率: {self.target_update}")

        for episode in range(max_episodes):
            current_pos = self.start
            state = self.preprocess_state(current_pos)
            total_reward = 0
            episode_steps = 0

            for _ in range(max_steps_per_episode):
                # 选择动作
                action = self.get_action(state, training=True)

                # 执行动作
                next_pos, reward, done = self.step(current_pos, action)
                next_state = self.preprocess_state(next_pos)

                # 存储经验
                self.store_experience(state, action, reward, next_state, done)

                # 更新计数器
                self.step_count += 1
                episode_steps += 1

                # 训练网络 (在预填充完成后)
                if self.step_count >= self.train_start:
                    self.replay()

                # 更新状态
                state = next_state
                current_pos = next_pos
                total_reward += reward

                if done:
                    break

            self.episode_rewards.append(total_reward)
            self.episode_lengths.append(episode_steps)

            # 打印训练进度
            if episode % 100 == 0:
                avg_reward = np.mean(self.episode_rewards[-100:]) if len(self.episode_rewards) >= 100 else np.mean(self.episode_rewards)
                avg_length = np.mean(self.episode_lengths[-100:]) if len(self.episode_lengths) >= 100 else np.mean(self.episode_lengths)
                print(f"回合 {episode:4d} | 平均奖励: {avg_reward:7.2f} | "
                      f"平均步数: {avg_length:6.1f} | Epsilon: {self.epsilon:.3f} | "
                      f"总步数: {self.step_count}")

        print("经典DQN训练完成!")

    def plan_path(self, max_steps=1000):
        """使用训练好的网络规划路径"""
        path = [self.start]
        current_pos = self.start

        for _ in range(max_steps):
            state = self.preprocess_state(current_pos)
            action = self.get_action(state, training=False)

            next_pos, _, done = self.step(current_pos, action)
            path.append(next_pos)
            current_pos = next_pos

            if done:
                print(f"找到路径，长度: {len(path)}")
                return path

            # 避免无限循环
            if len(path) > 3 and path[-1] == path[-3]:
                break

        print("未能找到路径")
        return None

    def visualize_training(self):
        """可视化训练过程"""
        _, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))

        # 奖励曲线
        ax1.plot(self.episode_rewards)
        ax1.set_title('训练奖励')
        ax1.set_xlabel('回合')
        ax1.set_ylabel('总奖励')
        ax1.grid(True)

        # 回合长度
        ax2.plot(self.episode_lengths)
        ax2.set_title('回合长度')
        ax2.set_xlabel('回合')
        ax2.set_ylabel('步数')
        ax2.grid(True)

        # 损失曲线
        if self.losses:
            ax3.plot(self.losses)
            ax3.set_title('训练损失')
            ax3.set_xlabel('更新次数')
            ax3.set_ylabel('损失')
            ax3.grid(True)

        # 移动平均奖励
        if len(self.episode_rewards) > 10:
            window = min(100, len(self.episode_rewards) // 10)
            moving_avg = np.convolve(self.episode_rewards,
                                   np.ones(window)/window, mode='valid')
            ax4.plot(moving_avg)
            ax4.set_title(f'移动平均奖励 (窗口={window})')
            ax4.set_xlabel('回合')
            ax4.set_ylabel('平均奖励')
            ax4.grid(True)

        plt.tight_layout()
        plt.show()

    def save_model(self, filepath):
        """保存模型"""
        torch.save({
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'losses': self.losses,
            'step_count': self.step_count
        }, filepath)
        print(f"经典DQN模型已保存到: {filepath}")

    def load_model(self, filepath):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.episode_rewards = checkpoint['episode_rewards']
        self.episode_lengths = checkpoint['episode_lengths']
        self.losses = checkpoint['losses']
        if 'step_count' in checkpoint:
            self.step_count = checkpoint['step_count']
        print(f"经典DQN模型已从 {filepath} 加载")

# 为了保持兼容性，创建别名
DeepQLearningPathPlanner = ClassicDQN

# 简化版Q-Learning规划器（用于兼容性）
class SimpleQLearningPathPlanner:
    """简化的Q-Learning路径规划器"""
    def __init__(self, start, goal, grid_map, max_episodes=500):
        self.start = start
        self.goal = goal
        self.grid_map = np.array(grid_map)
        self.max_episodes = max_episodes

        # Q表
        self.q_table = {}
        self.learning_rate = 0.1
        self.gamma = 0.9
        self.epsilon = 0.1

        # 动作空间
        self.actions = [(-1,-1), (-1,0), (-1,1), (0,-1), (0,1), (1,-1), (1,0), (1,1)]

        # 训练统计
        self.episode_rewards = []

    def get_q_value(self, state, action):
        """获取Q值"""
        if state not in self.q_table:
            self.q_table[state] = [0.0] * len(self.actions)
        return self.q_table[state][action]

    def update_q_value(self, state, action, reward, next_state):
        """更新Q值"""
        if state not in self.q_table:
            self.q_table[state] = [0.0] * len(self.actions)
        if next_state not in self.q_table:
            self.q_table[next_state] = [0.0] * len(self.actions)

        current_q = self.q_table[state][action]
        max_next_q = max(self.q_table[next_state])
        new_q = current_q + self.learning_rate * (reward + self.gamma * max_next_q - current_q)
        self.q_table[state][action] = new_q

    def choose_action(self, state):
        """选择动作"""
        if random.random() < self.epsilon:
            return random.randint(0, len(self.actions) - 1)

        if state not in self.q_table:
            return random.randint(0, len(self.actions) - 1)

        return np.argmax(self.q_table[state])

    def is_valid_position(self, pos):
        """检查位置是否有效"""
        x, y = pos
        if x < 0 or x >= self.grid_map.shape[0] or y < 0 or y >= self.grid_map.shape[1]:
            return False
        return self.grid_map[x, y] == 0

    def get_reward(self, pos):
        """计算奖励"""
        if pos == self.goal:
            return 100
        if not self.is_valid_position(pos):
            return -10
        return -1

    def train(self):
        """训练Q-Learning智能体"""
        for _ in range(self.max_episodes):
            current_pos = self.start
            total_reward = 0

            for _ in range(200):  # 最大步数
                action = self.choose_action(current_pos)
                dx, dy = self.actions[action]
                next_pos = (current_pos[0] + dx, current_pos[1] + dy)

                reward = self.get_reward(next_pos)
                total_reward += reward

                self.update_q_value(current_pos, action, reward, next_pos)

                if self.is_valid_position(next_pos):
                    current_pos = next_pos

                if current_pos == self.goal:
                    break

            self.episode_rewards.append(total_reward)

    def plan_path(self):
        """规划路径"""
        path = [self.start]
        current_pos = self.start

        for _ in range(500):  # 最大步数
            if current_pos == self.goal:
                return path

            if current_pos not in self.q_table:
                break

            action = np.argmax(self.q_table[current_pos])
            dx, dy = self.actions[action]
            next_pos = (current_pos[0] + dx, current_pos[1] + dy)

            if self.is_valid_position(next_pos):
                path.append(next_pos)
                current_pos = next_pos
            else:
                break

        return path if current_pos == self.goal else None

    def visualize_training(self):
        """可视化训练过程"""
        plt.figure(figsize=(10, 6))
        plt.plot(self.episode_rewards)
        plt.title('Q-Learning训练奖励')
        plt.xlabel('回合')
        plt.ylabel('总奖励')
        plt.grid(True)
        plt.show()
