"""
测试ACO路径连接问题
验证起点终点是否正确连接
"""
import numpy as np
import matplotlib.pyplot as plt
from ImprovedACO_PathPlanning import ACOPathPlanner

def test_aco_connection():
    """测试ACO路径连接"""
    print("🧪 测试ACO路径连接问题")
    print("=" * 50)
    
    # 创建简单测试环境
    grid = np.zeros((15, 15))
    
    # 添加一些障碍物
    grid[5:10, 7] = 1  # 垂直墙
    grid[7, 3:12] = 1  # 水平墙，但留出通道
    grid[7, 6:8] = 0   # 在墙中间留个缺口
    
    start = (2, 2)
    goal = (12, 12)
    
    print(f"环境大小: {grid.shape}")
    print(f"起点: {start}")
    print(f"终点: {goal}")
    print(f"障碍物数量: {np.sum(grid)}")
    
    # 创建ACO规划器
    aco = ACOPathPlanner(
        start=start,
        goal=goal,
        grid_map=grid,
        num_ants=10,
        max_iterations=30,
        step_size=0.5
    )
    
    print(f"\n🐜 ACO参数:")
    print(f"   蚂蚁数量: {aco.num_ants}")
    print(f"   最大迭代: {aco.max_iterations}")
    print(f"   步长: {aco.step_size}")
    
    # 运行优化
    print(f"\n🚀 开始ACO优化...")
    path = aco.optimize()
    
    # 详细分析结果
    print(f"\n📊 结果分析:")
    
    if path:
        print(f"✅ 返回了路径，长度: {len(path)}")
        print(f"   路径起点: {path[0]}")
        print(f"   路径终点: {path[-1]}")
        print(f"   期望起点: {(start[1], start[0])}")  # 注意坐标转换
        print(f"   期望终点: {(goal[1], goal[0])}")
        
        # 检查连接性
        expected_start = (start[1], start[0])  # (row, col)
        expected_goal = (goal[1], goal[0])
        
        start_connected = (path[0] == expected_start)
        goal_connected = (path[-1] == expected_goal)
        
        print(f"\n🔗 连接性检查:")
        print(f"   起点连接: {'✅' if start_connected else '❌'}")
        print(f"   终点连接: {'✅' if goal_connected else '❌'}")
        
        if not start_connected:
            print(f"   起点偏差: {np.array(path[0]) - np.array(expected_start)}")
        if not goal_connected:
            print(f"   终点偏差: {np.array(path[-1]) - np.array(expected_goal)}")
        
        # 检查路径有效性
        valid_path = True
        for i, point in enumerate(path):
            row, col = point
            if row < 0 or row >= grid.shape[0] or col < 0 or col >= grid.shape[1]:
                print(f"   ❌ 路径点 {i} 超出边界: {point}")
                valid_path = False
            elif grid[row, col] == 1:
                print(f"   ❌ 路径点 {i} 在障碍物上: {point}")
                valid_path = False
        
        if valid_path:
            print(f"   ✅ 路径所有点都有效")
        
        # 可视化结果
        plt.figure(figsize=(12, 5))
        
        # 左图：环境和路径
        plt.subplot(1, 2, 1)
        plt.imshow(grid, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 绘制路径
        path_array = np.array(path)
        plt.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=3, label='ACO Path')
        plt.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=4)
        
        # 标记起点和终点
        plt.plot(start[0], start[1], 'go', markersize=12, label='Start (Expected)')
        plt.plot(goal[0], goal[1], 'ro', markersize=12, label='Goal (Expected)')
        
        # 标记实际路径起终点
        plt.plot(path[0][1], path[0][0], 'g^', markersize=10, label='Path Start')
        plt.plot(path[-1][1], path[-1][0], 'r^', markersize=10, label='Path End')
        
        plt.title('ACO路径连接测试')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 右图：连接状态
        plt.subplot(1, 2, 2)
        
        # 创建连接状态图
        status_data = np.zeros((3, 2))
        status_data[0, 0] = 1 if start_connected else 0  # 起点连接
        status_data[1, 0] = 1 if goal_connected else 0   # 终点连接
        status_data[2, 0] = 1 if valid_path else 0       # 路径有效
        
        plt.imshow(status_data, cmap='RdYlGn', aspect='auto')
        
        labels = ['起点连接', '终点连接', '路径有效']
        for i, label in enumerate(labels):
            status = '✅' if status_data[i, 0] == 1 else '❌'
            plt.text(0, i, f'{label}: {status}', ha='center', va='center', 
                    fontsize=12, fontweight='bold')
        
        plt.xlim(-0.5, 0.5)
        plt.ylim(-0.5, 2.5)
        plt.xticks([])
        plt.yticks(range(3), labels)
        plt.title('连接状态检查')
        
        plt.tight_layout()
        plt.show()
        
        return start_connected and goal_connected and valid_path
        
    else:
        print(f"❌ 没有返回路径")
        
        # 可视化失败情况
        plt.figure(figsize=(8, 6))
        plt.imshow(grid, cmap='gray_r', origin='upper', alpha=0.7)
        
        plt.plot(start[0], start[1], 'go', markersize=12, label='Start')
        plt.plot(goal[0], goal[1], 'ro', markersize=12, label='Goal')
        
        plt.title('ACO路径规划失败')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()
        
        return False

def test_multiple_scenarios():
    """测试多种场景"""
    print(f"\n🔬 多场景测试")
    print("=" * 30)
    
    scenarios = [
        {
            'name': '简单直线',
            'grid_size': (10, 10),
            'start': (1, 1),
            'goal': (8, 8),
            'obstacles': []
        },
        {
            'name': '单一障碍',
            'grid_size': (12, 12),
            'start': (1, 1),
            'goal': (10, 10),
            'obstacles': [(5, 5, 7, 7)]  # (x1, y1, x2, y2)
        },
        {
            'name': '复杂迷宫',
            'grid_size': (15, 15),
            'start': (1, 1),
            'goal': (13, 13),
            'obstacles': [(5, 0, 5, 10), (10, 5, 15, 5)]  # L形障碍
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n场景 {i}: {scenario['name']}")
        print("-" * 20)
        
        # 创建环境
        grid = np.zeros(scenario['grid_size'])
        for obs in scenario['obstacles']:
            if len(obs) == 4:  # 矩形障碍物
                x1, y1, x2, y2 = obs
                grid[y1:y2, x1:x2] = 1
        
        start = scenario['start']
        goal = scenario['goal']
        
        # 测试ACO
        aco = ACOPathPlanner(start, goal, grid, num_ants=8, max_iterations=20)
        path = aco.optimize()
        
        if path:
            expected_start = (start[1], start[0])
            expected_goal = (goal[1], goal[0])
            start_ok = (path[0] == expected_start)
            goal_ok = (path[-1] == expected_goal)
            
            result = {
                'scenario': scenario['name'],
                'success': True,
                'start_connected': start_ok,
                'goal_connected': goal_ok,
                'path_length': len(path)
            }
            
            print(f"✅ 成功: 起点{'✅' if start_ok else '❌'} 终点{'✅' if goal_ok else '❌'}")
        else:
            result = {
                'scenario': scenario['name'],
                'success': False,
                'start_connected': False,
                'goal_connected': False,
                'path_length': 0
            }
            print(f"❌ 失败")
        
        results.append(result)
    
    # 总结
    print(f"\n📊 测试总结:")
    successful = [r for r in results if r['success']]
    fully_connected = [r for r in successful if r['start_connected'] and r['goal_connected']]
    
    print(f"   成功率: {len(successful)}/{len(results)} ({len(successful)/len(results)*100:.1f}%)")
    print(f"   完全连接: {len(fully_connected)}/{len(successful)} ({len(fully_connected)/len(successful)*100:.1f}% of successful)")
    
    return results

def main():
    """主测试函数"""
    print("🔧 ACO路径连接问题诊断和修复测试")
    print("=" * 60)
    
    # 单个详细测试
    success = test_aco_connection()
    
    # 多场景测试
    results = test_multiple_scenarios()
    
    print(f"\n🎯 总结:")
    if success:
        print("✅ ACO路径连接问题已修复！")
    else:
        print("⚠️ ACO路径连接仍有问题，需要进一步调试")
    
    print("\n💡 如果仍有问题，可能的原因:")
    print("1. 步长太大，跳过了目标点")
    print("2. 坐标系转换问题")
    print("3. 收敛条件太宽松")
    print("4. 障碍物阻挡了直接路径")

if __name__ == "__main__":
    main()
