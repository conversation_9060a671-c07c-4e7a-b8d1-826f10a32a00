"""
简单测试脚本
"""
import sys
print("Python版本:", sys.version)

try:
    import numpy as np
    print("✅ NumPy导入成功")
except ImportError as e:
    print("❌ NumPy导入失败:", e)

try:
    import matplotlib.pyplot as plt
    print("✅ Matplotlib导入成功")
except ImportError as e:
    print("❌ Matplotlib导入失败:", e)

try:
    import torch
    print("✅ PyTorch导入成功")
    print("PyTorch版本:", torch.__version__)
    print("CUDA可用:", torch.cuda.is_available())
except ImportError as e:
    print("❌ PyTorch导入失败:", e)

try:
    from DeepRL_PathPlanning import SimpleQLearningPathPlanner
    print("✅ SimpleQLearningPathPlanner导入成功")
except ImportError as e:
    print("❌ SimpleQLearningPathPlanner导入失败:", e)

try:
    from DeepRL_PathPlanning import ClassicDQN
    print("✅ ClassicDQN导入成功")
except ImportError as e:
    print("❌ ClassicDQN导入失败:", e)

# 测试简单Q-Learning
print("\n测试简单Q-Learning...")
try:
    import numpy as np
    
    # 创建简单环境
    grid = np.zeros((10, 10))
    grid[5, :] = 1  # 添加障碍
    
    # 创建Q-Learning智能体
    qlearning = SimpleQLearningPathPlanner(
        start=(1, 1),
        goal=(8, 8),
        grid_map=grid,
        max_episodes=10
    )
    
    print("Q-Learning智能体创建成功")
    
    # 简短训练
    qlearning.train()
    print("Q-Learning训练完成")
    
    # 路径规划
    path = qlearning.plan_path()
    if path:
        print(f"✅ Q-Learning找到路径，长度: {len(path)}")
    else:
        print("❌ Q-Learning未找到路径")
        
except Exception as e:
    print(f"❌ Q-Learning测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n测试完成")
