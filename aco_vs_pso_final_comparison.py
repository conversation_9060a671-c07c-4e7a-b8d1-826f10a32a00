"""
ACO vs PSO 最终对比分析
使用改进的ACO算法与PSO进行性能对比
"""
import numpy as np
import matplotlib.pyplot as plt
import time
from ImprovedACO_PathPlanning import ACOPathPlanner
from PSO_PathPlanning import PSOPathPlanner

def create_test_environment():
    """创建标准测试环境"""
    grid = np.zeros((25, 25))
    
    # 添加复杂障碍物
    grid[5:20, 10] = 1    # 垂直墙
    grid[12, 5:20] = 1    # 水平墙
    grid[3:7, 3:7] = 1    # 左上角块
    grid[18:22, 18:22] = 1  # 右下角块
    
    # 添加一些随机障碍物
    np.random.seed(42)
    for _ in range(8):
        x, y = np.random.randint(0, 25, 2)
        if grid[x, y] == 0:
            grid[x:x+2, y:y+2] = 1
    
    start = (2, 2)
    goal = (22, 22)
    
    return grid, start, goal

def benchmark_algorithms():
    """对比ACO和PSO性能"""
    print("🔬 ACO vs PSO 性能对比测试")
    print("=" * 50)
    
    # 创建测试环境
    grid, start, goal = create_test_environment()
    print(f"测试环境: {grid.shape}")
    print(f"起点: {start}, 终点: {goal}")
    print(f"障碍物数量: {np.sum(grid)}")
    
    # 算法配置
    algorithms = {
        "ACO": {
            'class': ACOPathPlanner,
            'params': {'num_ants': 15, 'max_iterations': 30, 'step_size': 0.8}
        },
        "PSO": {
            'class': PSOPathPlanner,
            'params': {'n_particles': 20, 'max_iter': 50}
        }
    }
    
    results = {}
    paths = {}
    
    # 测试每个算法
    for alg_name, config in algorithms.items():
        print(f"\n测试 {alg_name}...")
        
        # 运行多次测试
        alg_results = []
        alg_paths = []
        
        for run in range(3):
            print(f"  运行 {run + 1}/3")
            
            start_time = time.time()
            
            try:
                planner = config['class'](start, goal, grid, **config['params'])
                
                if alg_name == "ACO":
                    path = planner.optimize()
                else:  # PSO
                    raw_path = planner.optimize()
                    path = planner.get_path_as_points()
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                if path:
                    path_length = len(path)
                    reached_goal = (path[-1] == goal)
                    
                    # 计算实际距离
                    actual_distance = 0
                    for i in range(len(path) - 1):
                        p1, p2 = path[i], path[i+1]
                        actual_distance += np.linalg.norm(np.array(p2) - np.array(p1))
                    
                    alg_results.append({
                        'success': True,
                        'time': execution_time,
                        'path_length': path_length,
                        'actual_distance': actual_distance,
                        'reached_goal': reached_goal
                    })
                    alg_paths.append(path)
                    
                    print(f"    ✅ 成功: {execution_time:.2f}s, 长度: {path_length}")
                else:
                    alg_results.append({
                        'success': False,
                        'time': execution_time,
                        'path_length': 0,
                        'actual_distance': float('inf'),
                        'reached_goal': False
                    })
                    alg_paths.append(None)
                    print(f"    ❌ 失败: {execution_time:.2f}s")
                    
            except Exception as e:
                print(f"    💥 错误: {e}")
                alg_results.append({
                    'success': False,
                    'time': float('inf'),
                    'path_length': 0,
                    'actual_distance': float('inf'),
                    'reached_goal': False
                })
                alg_paths.append(None)
        
        results[alg_name] = alg_results
        paths[alg_name] = alg_paths
    
    return grid, start, goal, results, paths

def analyze_and_visualize(grid, start, goal, results, paths):
    """分析结果并可视化"""
    # 分析结果
    analysis = {}
    for alg_name, alg_results in results.items():
        successful_runs = [r for r in alg_results if r['success']]
        
        if successful_runs:
            analysis[alg_name] = {
                'success_rate': len(successful_runs) / len(alg_results),
                'avg_time': np.mean([r['time'] for r in successful_runs]),
                'avg_path_length': np.mean([r['path_length'] for r in successful_runs]),
                'std_time': np.std([r['time'] for r in successful_runs]),
            }
        else:
            analysis[alg_name] = {
                'success_rate': 0,
                'avg_time': float('inf'),
                'avg_path_length': 0,
                'std_time': 0,
            }
    
    # 可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 路径对比
    colors = ['red', 'blue']
    for i, (alg_name, alg_paths) in enumerate(paths.items()):
        ax = axes[0, i]
        
        # 显示地图
        ax.imshow(grid, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 绘制路径
        successful_paths = [p for p in alg_paths if p is not None]
        
        if successful_paths:
            for j, path in enumerate(successful_paths):
                path_array = np.array(path)
                alpha = 0.4 + 0.6 * (j == 0)
                ax.plot(path_array[:, 1], path_array[:, 0], 
                       color=colors[i], linewidth=2, alpha=alpha)
        
        # 标记起点和终点
        ax.plot(start[1], start[0], 'go', markersize=12, label='Start')
        ax.plot(goal[1], goal[0], 'ro', markersize=12, label='Goal')
        
        ax.set_title(f'{alg_name}\n成功率: {analysis[alg_name]["success_rate"]*100:.0f}%')
        ax.legend()
        ax.axis('off')
    
    # 2. 性能对比
    ax = axes[0, 2]
    alg_names = list(analysis.keys())
    times = [analysis[alg]['avg_time'] for alg in alg_names]
    time_stds = [analysis[alg]['std_time'] for alg in alg_names]
    
    bars = ax.bar(alg_names, times, yerr=time_stds, capsize=5, 
                  color=colors[:len(alg_names)], alpha=0.7)
    ax.set_ylabel('平均执行时间 (秒)')
    ax.set_title('执行时间对比')
    ax.grid(True, alpha=0.3)
    
    for bar, time_val in zip(bars, times):
        if time_val != float('inf'):
            ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.1,
                   f'{time_val:.2f}s', ha='center', va='bottom')
    
    # 3. 路径长度对比
    ax = axes[1, 0]
    path_lengths = [analysis[alg]['avg_path_length'] for alg in alg_names]
    
    bars = ax.bar(alg_names, path_lengths, color=colors[:len(alg_names)], alpha=0.7)
    ax.set_ylabel('平均路径长度')
    ax.set_title('路径长度对比')
    ax.grid(True, alpha=0.3)
    
    for bar, length in zip(bars, path_lengths):
        if length > 0:
            ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.5,
                   f'{length:.0f}', ha='center', va='bottom')
    
    # 4. 成功率对比
    ax = axes[1, 1]
    success_rates = [analysis[alg]['success_rate'] * 100 for alg in alg_names]
    
    bars = ax.bar(alg_names, success_rates, color=colors[:len(alg_names)], alpha=0.7)
    ax.set_ylabel('成功率 (%)')
    ax.set_title('成功率对比')
    ax.set_ylim(0, 110)
    ax.grid(True, alpha=0.3)
    
    for bar, rate in zip(bars, success_rates):
        ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 2,
               f'{rate:.0f}%', ha='center', va='bottom')
    
    # 5. 总结表格
    ax = axes[1, 2]
    ax.axis('off')
    
    table_data = []
    for alg_name in alg_names:
        stats = analysis[alg_name]
        table_data.append([
            alg_name,
            f"{stats['avg_time']:.2f}±{stats['std_time']:.2f}",
            f"{stats['avg_path_length']:.0f}",
            f"{stats['success_rate']*100:.0f}%"
        ])
    
    table = ax.table(cellText=table_data,
                    colLabels=['算法', '时间(s)', '路径长度', '成功率'],
                    cellLoc='center',
                    loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(table_data[0])):
        table[(0, i)].set_facecolor('#4ECDC4')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    ax.set_title('性能统计表', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.suptitle('ACO vs PSO 算法性能对比', fontsize=16, fontweight='bold', y=0.98)
    plt.show()
    
    return analysis

def print_summary(analysis):
    """打印总结"""
    print("\n" + "=" * 50)
    print("📊 对比总结")
    print("=" * 50)
    
    aco_stats = analysis['ACO']
    pso_stats = analysis['PSO']
    
    print(f"\n🕒 执行时间:")
    print(f"   ACO: {aco_stats['avg_time']:.2f}±{aco_stats['std_time']:.2f}秒")
    print(f"   PSO: {pso_stats['avg_time']:.2f}±{pso_stats['std_time']:.2f}秒")
    
    if pso_stats['avg_time'] < aco_stats['avg_time']:
        speedup = (aco_stats['avg_time'] / pso_stats['avg_time'] - 1) * 100
        print(f"   🏆 PSO比ACO快 {speedup:.1f}%")
    else:
        speedup = (pso_stats['avg_time'] / aco_stats['avg_time'] - 1) * 100
        print(f"   🏆 ACO比PSO快 {speedup:.1f}%")
    
    print(f"\n✅ 成功率:")
    print(f"   ACO: {aco_stats['success_rate']*100:.1f}%")
    print(f"   PSO: {pso_stats['success_rate']*100:.1f}%")
    
    print(f"\n📏 路径长度:")
    print(f"   ACO: {aco_stats['avg_path_length']:.1f}")
    print(f"   PSO: {pso_stats['avg_path_length']:.1f}")
    
    print(f"\n🎯 推荐:")
    if pso_stats['avg_time'] < aco_stats['avg_time'] and pso_stats['success_rate'] >= aco_stats['success_rate']:
        print(f"   推荐使用PSO：速度更快，成功率相当")
    elif aco_stats['success_rate'] > pso_stats['success_rate']:
        print(f"   推荐使用ACO：成功率更高，路径质量更好")
    else:
        print(f"   两种算法各有优势，根据具体需求选择")

def main():
    """主函数"""
    # 运行对比测试
    grid, start, goal, results, paths = benchmark_algorithms()
    
    # 分析和可视化
    analysis = analyze_and_visualize(grid, start, goal, results, paths)
    
    # 打印总结
    print_summary(analysis)
    
    print(f"\n🎉 对比测试完成！")

if __name__ == "__main__":
    main()
