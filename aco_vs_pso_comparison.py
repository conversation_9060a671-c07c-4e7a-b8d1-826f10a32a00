"""
ACO vs PSO 算法性能对比分析
"""
import numpy as np
import matplotlib.pyplot as plt
import time
from ACO_PathPlanning import ACOPathPlanner
from PSO_PathPlanning import PSOPathPlanner

def create_test_environments():
    """创建不同复杂度的测试环境"""
    environments = {}
    
    # 简单环境
    simple_grid = np.zeros((10, 10))
    simple_grid[3:7, 5] = 1  # 简单障碍物
    environments['简单环境'] = {
        'grid': simple_grid,
        'start': (1, 1),
        'goal': (8, 8),
        'description': '10x10网格，简单障碍物'
    }
    
    # 中等环境
    medium_grid = np.zeros((20, 20))
    medium_grid[5:15, 8] = 1  # 垂直墙
    medium_grid[10, 5:15] = 1  # 水平墙
    medium_grid[3:6, 3:6] = 1  # 小块障碍
    environments['中等环境'] = {
        'grid': medium_grid,
        'start': (1, 1),
        'goal': (18, 18),
        'description': '20x20网格，中等复杂度'
    }
    
    # 复杂环境
    complex_grid = np.zeros((30, 30))
    # 创建迷宫式障碍物
    complex_grid[5:25, 10] = 1
    complex_grid[5:25, 20] = 1
    complex_grid[10, 5:25] = 1
    complex_grid[20, 5:25] = 1
    complex_grid[15, 10:20] = 1
    # 添加随机障碍物
    np.random.seed(42)
    for _ in range(20):
        x, y = np.random.randint(0, 30, 2)
        if complex_grid[x, y] == 0:
            complex_grid[x:x+2, y:y+2] = 1
    
    environments['复杂环境'] = {
        'grid': complex_grid,
        'start': (1, 1),
        'goal': (28, 28),
        'description': '30x30网格，复杂迷宫'
    }
    
    return environments

def benchmark_algorithm(algorithm_class, env_data, algorithm_name, **kwargs):
    """测试单个算法的性能"""
    grid = env_data['grid']
    start = env_data['start']
    goal = env_data['goal']
    
    print(f"  测试 {algorithm_name}...")
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 创建规划器
        if algorithm_name == 'ACO':
            planner = algorithm_class(start, goal, grid, **kwargs)
            path = planner.optimize()
        else:  # PSO
            planner = algorithm_class(start, goal, grid, **kwargs)
            raw_path = planner.optimize()
            path = planner.get_path_as_points()
        
        # 记录结束时间
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 分析结果
        if path:
            path_length = len(path)
            # 计算实际距离
            if algorithm_name == 'PSO' and raw_path is not None:
                actual_distance = 0
                for i in range(len(raw_path) - 1):
                    actual_distance += np.linalg.norm(raw_path[i+1] - raw_path[i])
            else:
                actual_distance = 0
                for i in range(len(path) - 1):
                    p1, p2 = path[i], path[i+1]
                    actual_distance += np.linalg.norm(np.array(p2) - np.array(p1))
            
            success = True
            reached_goal = (path[-1] == goal)
        else:
            path_length = 0
            actual_distance = float('inf')
            success = False
            reached_goal = False
        
        return {
            'success': success,
            'execution_time': execution_time,
            'path_length': path_length,
            'actual_distance': actual_distance,
            'reached_goal': reached_goal,
            'planner': planner
        }
        
    except Exception as e:
        print(f"    {algorithm_name} 执行出错: {e}")
        return {
            'success': False,
            'execution_time': float('inf'),
            'path_length': 0,
            'actual_distance': float('inf'),
            'reached_goal': False,
            'planner': None
        }

def run_comprehensive_comparison():
    """运行综合对比测试"""
    print("🔬 ACO vs PSO 算法性能对比测试")
    print("=" * 60)
    
    # 创建测试环境
    environments = create_test_environments()
    
    # 算法配置
    algorithms = {
        'ACO': {
            'class': ACOPathPlanner,
            'params': {'n_ants': 5, 'max_iter': 10}  # 减少参数以加快测试
        },
        'PSO': {
            'class': PSOPathPlanner,
            'params': {'n_particles': 20, 'max_iter': 50}
        }
    }
    
    # 存储结果
    results = {}
    
    # 对每个环境进行测试
    for env_name, env_data in environments.items():
        print(f"\n📍 测试环境: {env_name}")
        print(f"   {env_data['description']}")
        print(f"   起点: {env_data['start']}, 终点: {env_data['goal']}")
        
        results[env_name] = {}
        
        # 测试每个算法
        for alg_name, alg_config in algorithms.items():
            result = benchmark_algorithm(
                alg_config['class'], 
                env_data, 
                alg_name, 
                **alg_config['params']
            )
            results[env_name][alg_name] = result
            
            # 打印结果
            if result['success']:
                print(f"    ✅ {alg_name}: {result['execution_time']:.2f}s, "
                      f"路径长度: {result['path_length']}, "
                      f"实际距离: {result['actual_distance']:.2f}")
            else:
                print(f"    ❌ {alg_name}: 失败 ({result['execution_time']:.2f}s)")
    
    return results, environments

def visualize_comparison_results(results, environments):
    """可视化对比结果"""
    env_names = list(environments.keys())
    alg_names = ['ACO', 'PSO']
    
    # 创建对比图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 执行时间对比
    ax = axes[0, 0]
    aco_times = [results[env][alg_names[0]]['execution_time'] for env in env_names]
    pso_times = [results[env][alg_names[1]]['execution_time'] for env in env_names]
    
    x = np.arange(len(env_names))
    width = 0.35
    
    ax.bar(x - width/2, aco_times, width, label='ACO', color='orange', alpha=0.7)
    ax.bar(x + width/2, pso_times, width, label='PSO', color='blue', alpha=0.7)
    
    ax.set_xlabel('测试环境')
    ax.set_ylabel('执行时间 (秒)')
    ax.set_title('执行时间对比')
    ax.set_xticks(x)
    ax.set_xticklabels(env_names, rotation=45)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 2. 路径长度对比
    ax = axes[0, 1]
    aco_lengths = [results[env][alg_names[0]]['path_length'] if results[env][alg_names[0]]['success'] else 0 for env in env_names]
    pso_lengths = [results[env][alg_names[1]]['path_length'] if results[env][alg_names[1]]['success'] else 0 for env in env_names]
    
    ax.bar(x - width/2, aco_lengths, width, label='ACO', color='orange', alpha=0.7)
    ax.bar(x + width/2, pso_lengths, width, label='PSO', color='blue', alpha=0.7)
    
    ax.set_xlabel('测试环境')
    ax.set_ylabel('路径长度 (步数)')
    ax.set_title('路径长度对比')
    ax.set_xticks(x)
    ax.set_xticklabels(env_names, rotation=45)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 3. 成功率对比
    ax = axes[0, 2]
    aco_success = [1 if results[env][alg_names[0]]['success'] else 0 for env in env_names]
    pso_success = [1 if results[env][alg_names[1]]['success'] else 0 for env in env_names]
    
    ax.bar(x - width/2, aco_success, width, label='ACO', color='orange', alpha=0.7)
    ax.bar(x + width/2, pso_success, width, label='PSO', color='blue', alpha=0.7)
    
    ax.set_xlabel('测试环境')
    ax.set_ylabel('成功率')
    ax.set_title('算法成功率对比')
    ax.set_xticks(x)
    ax.set_xticklabels(env_names, rotation=45)
    ax.set_ylim(0, 1.1)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 4. 速度效率分析
    ax = axes[1, 0]
    speed_ratios = []
    labels = []
    
    for env in env_names:
        aco_time = results[env]['ACO']['execution_time']
        pso_time = results[env]['PSO']['execution_time']
        
        if aco_time != float('inf') and pso_time != float('inf'):
            ratio = aco_time / pso_time
            speed_ratios.append(ratio)
            labels.append(env)
    
    colors = ['red' if r > 1 else 'green' for r in speed_ratios]
    bars = ax.bar(range(len(speed_ratios)), speed_ratios, color=colors, alpha=0.7)
    
    ax.axhline(y=1, color='black', linestyle='--', alpha=0.5)
    ax.set_xlabel('测试环境')
    ax.set_ylabel('ACO时间 / PSO时间')
    ax.set_title('速度比较 (>1表示ACO更慢)')
    ax.set_xticks(range(len(labels)))
    ax.set_xticklabels(labels, rotation=45)
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, ratio in zip(bars, speed_ratios):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                f'{ratio:.2f}', ha='center', va='bottom')
    
    # 5. 算法特性雷达图
    ax = axes[1, 1]
    ax.remove()
    ax = fig.add_subplot(2, 3, 5, projection='polar')
    
    categories = ['速度', '路径质量', '成功率', '稳定性']
    
    # 计算各项指标 (归一化到0-1)
    aco_scores = [
        1 - np.mean([results[env]['ACO']['execution_time'] for env in env_names if results[env]['ACO']['execution_time'] != float('inf')]) / 10,  # 速度 (反向)
        np.mean([1/results[env]['ACO']['path_length'] if results[env]['ACO']['success'] and results[env]['ACO']['path_length'] > 0 else 0 for env in env_names]) * 100,  # 路径质量
        np.mean([results[env]['ACO']['success'] for env in env_names]),  # 成功率
        0.7  # 稳定性 (经验值)
    ]
    
    pso_scores = [
        1 - np.mean([results[env]['PSO']['execution_time'] for env in env_names if results[env]['PSO']['execution_time'] != float('inf')]) / 10,  # 速度 (反向)
        np.mean([1/results[env]['PSO']['path_length'] if results[env]['PSO']['success'] and results[env]['PSO']['path_length'] > 0 else 0 for env in env_names]) * 100,  # 路径质量
        np.mean([results[env]['PSO']['success'] for env in env_names]),  # 成功率
        0.8  # 稳定性 (经验值)
    ]
    
    # 归一化
    aco_scores = [max(0, min(1, score)) for score in aco_scores]
    pso_scores = [max(0, min(1, score)) for score in pso_scores]
    
    angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合
    
    aco_scores += aco_scores[:1]
    pso_scores += pso_scores[:1]
    
    ax.plot(angles, aco_scores, 'o-', linewidth=2, label='ACO', color='orange')
    ax.fill(angles, aco_scores, alpha=0.25, color='orange')
    ax.plot(angles, pso_scores, 'o-', linewidth=2, label='PSO', color='blue')
    ax.fill(angles, pso_scores, alpha=0.25, color='blue')
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories)
    ax.set_ylim(0, 1)
    ax.set_title('算法综合性能对比', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    # 6. 总结表格
    ax = axes[1, 2]
    ax.axis('off')
    
    # 创建总结数据
    summary_data = []
    for env in env_names:
        aco_result = results[env]['ACO']
        pso_result = results[env]['PSO']
        
        summary_data.append([
            env,
            f"{aco_result['execution_time']:.2f}s" if aco_result['execution_time'] != float('inf') else "失败",
            f"{pso_result['execution_time']:.2f}s" if pso_result['execution_time'] != float('inf') else "失败",
            "ACO" if aco_result['execution_time'] < pso_result['execution_time'] else "PSO"
        ])
    
    table = ax.table(cellText=summary_data,
                    colLabels=['环境', 'ACO时间', 'PSO时间', '更快算法'],
                    cellLoc='center',
                    loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(summary_data[0])):
        table[(0, i)].set_facecolor('#4ECDC4')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    ax.set_title('性能总结', fontsize=14, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.show()

def print_analysis_summary(results):
    """打印分析总结"""
    print("\n" + "="*60)
    print("📊 性能分析总结")
    print("="*60)
    
    env_names = list(results.keys())
    
    # 计算平均性能
    aco_avg_time = np.mean([results[env]['ACO']['execution_time'] for env in env_names if results[env]['ACO']['execution_time'] != float('inf')])
    pso_avg_time = np.mean([results[env]['PSO']['execution_time'] for env in env_names if results[env]['PSO']['execution_time'] != float('inf')])
    
    aco_success_rate = np.mean([results[env]['ACO']['success'] for env in env_names])
    pso_success_rate = np.mean([results[env]['PSO']['success'] for env in env_names])
    
    print(f"🕒 平均执行时间:")
    print(f"   ACO: {aco_avg_time:.2f}秒")
    print(f"   PSO: {pso_avg_time:.2f}秒")
    print(f"   速度优势: {'PSO' if pso_avg_time < aco_avg_time else 'ACO'} 快 {abs(aco_avg_time - pso_avg_time):.2f}秒")
    
    print(f"\n✅ 成功率:")
    print(f"   ACO: {aco_success_rate*100:.1f}%")
    print(f"   PSO: {pso_success_rate*100:.1f}%")
    
    print(f"\n🎯 算法特点总结:")
    print(f"   ACO (蚁群算法):")
    print(f"     - 优点: 能找到较优路径，适合复杂环境")
    print(f"     - 缺点: 收敛速度慢，计算量大")
    print(f"     - 适用: 对路径质量要求高的场景")
    
    print(f"   PSO (粒子群算法):")
    print(f"     - 优点: 收敛速度快，实现简单")
    print(f"     - 缺点: 可能陷入局部最优")
    print(f"     - 适用: 对实时性要求高的场景")
    
    print(f"\n💡 建议:")
    if pso_avg_time < aco_avg_time:
        print(f"   - PSO在速度上有明显优势，适合实时路径规划")
        print(f"   - 如果对路径质量要求不是特别高，推荐使用PSO")
    else:
        print(f"   - ACO在路径质量上可能更好，但速度较慢")
        print(f"   - 可以考虑减少ACO的蚂蚁数量和迭代次数来提高速度")

def main():
    """主函数"""
    print("🚀 开始ACO vs PSO性能对比测试...")
    
    # 运行对比测试
    results, environments = run_comprehensive_comparison()
    
    # 可视化结果
    visualize_comparison_results(results, environments)
    
    # 打印分析总结
    print_analysis_summary(results)
    
    print(f"\n🎉 测试完成！")

if __name__ == "__main__":
    main()
