import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Categorical
import matplotlib.pyplot as plt
from collections import deque
import random

class ActorCriticNetwork(nn.Module):
    """Actor-Critic网络架构"""
    def __init__(self, input_channels=3, action_size=8, hidden_size=512):
        super(ActorCriticNetwork, self).__init__()
        
        # 共享卷积层
        self.conv1 = nn.Conv2d(input_channels, 32, kernel_size=8, stride=4)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=4, stride=2)
        self.conv3 = nn.Conv2d(64, 64, kernel_size=3, stride=1)
        
        # 计算卷积后的特征图大小
        self.feature_size = self._get_conv_output_size()
        
        # 共享全连接层
        self.fc_shared = nn.Linear(self.feature_size, hidden_size)
        
        # Actor网络（策略网络）
        self.actor_fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.actor_fc2 = nn.Linear(hidden_size // 2, action_size)
        
        # Critic网络（价值网络）
        self.critic_fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.critic_fc2 = nn.Linear(hidden_size // 2, 1)
        
        self.dropout = nn.Dropout(0.3)
        
    def _get_conv_output_size(self):
        """计算卷积层输出大小"""
        x = torch.zeros(1, 3, 84, 84)
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))
        return x.view(1, -1).size(1)
    
    def forward(self, x):
        # 共享特征提取
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))
        x = x.view(x.size(0), -1)
        x = F.relu(self.fc_shared(x))
        x = self.dropout(x)
        
        # Actor分支（策略）
        actor_x = F.relu(self.actor_fc1(x))
        actor_x = self.dropout(actor_x)
        action_logits = self.actor_fc2(actor_x)
        action_probs = F.softmax(action_logits, dim=-1)
        
        # Critic分支（价值）
        critic_x = F.relu(self.critic_fc1(x))
        critic_x = self.dropout(critic_x)
        state_value = self.critic_fc2(critic_x)
        
        return action_probs, state_value

class PPOPathPlanner:
    """基于PPO的路径规划器"""
    def __init__(self, start, goal, grid_map,
                 learning_rate=3e-4,
                 gamma=0.99,
                 gae_lambda=0.95,
                 clip_epsilon=0.2,
                 entropy_coef=0.01,
                 value_coef=0.5,
                 max_grad_norm=0.5,
                 ppo_epochs=4,
                 batch_size=64):
        
        self.start = start
        self.goal = goal
        self.grid_map = np.array(grid_map)
        self.map_shape = self.grid_map.shape
        
        # PPO超参数
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_epsilon = clip_epsilon
        self.entropy_coef = entropy_coef
        self.value_coef = value_coef
        self.max_grad_norm = max_grad_norm
        self.ppo_epochs = ppo_epochs
        self.batch_size = batch_size
        
        # 动作空间：8个方向移动
        self.actions = [(-1,-1), (-1,0), (-1,1), (0,-1), (0,1), (1,-1), (1,0), (1,1)]
        self.action_size = len(self.actions)
        
        # 设备选择
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"PPO使用设备: {self.device}")
        
        # 网络初始化
        self.network = ActorCriticNetwork(
            input_channels=3, 
            action_size=self.action_size
        ).to(self.device)
        
        self.optimizer = optim.Adam(self.network.parameters(), lr=learning_rate)
        
        # 经验缓冲区
        self.memory = {
            'states': [],
            'actions': [],
            'rewards': [],
            'values': [],
            'log_probs': [],
            'dones': []
        }
        
        # 训练统计
        self.episode_rewards = []
        self.episode_lengths = []
        self.policy_losses = []
        self.value_losses = []
        self.entropy_losses = []
        
    def preprocess_state(self, position):
        """预处理状态，将环境信息转换为网络输入"""
        window_size = 84
        half_window = window_size // 2
        
        # 创建三通道状态表示
        state = np.zeros((3, window_size, window_size), dtype=np.float32)
        
        # 计算窗口在全局地图中的位置
        start_x = max(0, position[0] - half_window)
        end_x = min(self.map_shape[0], position[0] + half_window)
        start_y = max(0, position[1] - half_window)
        end_y = min(self.map_shape[1], position[1] + half_window)
        
        # 计算在状态数组中的位置
        state_start_x = half_window - (position[0] - start_x)
        state_end_x = state_start_x + (end_x - start_x)
        state_start_y = half_window - (position[1] - start_y)
        state_end_y = state_start_y + (end_y - start_y)
        
        # 通道0：障碍物信息
        state[0, state_start_x:state_end_x, state_start_y:state_end_y] = \
            self.grid_map[start_x:end_x, start_y:end_y]
            
        # 通道1：目标位置信息
        goal_x = self.goal[0] - position[0] + half_window
        goal_y = self.goal[1] - position[1] + half_window
        if 0 <= goal_x < window_size and 0 <= goal_y < window_size:
            state[1, goal_x, goal_y] = 1.0
            
        # 通道2：当前位置信息
        state[2, half_window, half_window] = 1.0
        
        return state
        
    def get_action_and_value(self, state):
        """获取动作和价值"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            action_probs, state_value = self.network(state_tensor)
            
        # 从概率分布中采样动作
        dist = Categorical(action_probs)
        action = dist.sample()
        log_prob = dist.log_prob(action)
        
        return action.item(), log_prob.item(), state_value.item()
        
    def is_valid_position(self, pos):
        """检查位置是否有效"""
        x, y = pos
        if x < 0 or x >= self.map_shape[0] or y < 0 or y >= self.map_shape[1]:
            return False
        return self.grid_map[x, y] == 0
        
    def get_reward(self, current_pos, next_pos, done):
        """计算奖励函数"""
        # 基础移动惩罚
        reward = -0.01
        
        # 到达目标的奖励
        if done and next_pos == self.goal:
            reward += 100.0
            
        # 撞墙惩罚
        if not self.is_valid_position(next_pos):
            reward -= 10.0
            
        # 距离奖励（鼓励向目标移动）
        current_dist = np.linalg.norm(np.array(current_pos) - np.array(self.goal))
        next_dist = np.linalg.norm(np.array(next_pos) - np.array(self.goal))
        reward += (current_dist - next_dist) * 0.1
        
        return reward
        
    def step(self, current_pos, action):
        """执行动作并返回下一个状态"""
        dx, dy = self.actions[action]
        next_pos = (current_pos[0] + dx, current_pos[1] + dy)
        
        # 检查是否到达目标
        done = (next_pos == self.goal)
        
        # 如果位置无效，保持在当前位置
        if not self.is_valid_position(next_pos):
            next_pos = current_pos
            
        reward = self.get_reward(current_pos, next_pos, done)
        
        return next_pos, reward, done
        
    def store_experience(self, state, action, reward, value, log_prob, done):
        """存储经验"""
        self.memory['states'].append(state)
        self.memory['actions'].append(action)
        self.memory['rewards'].append(reward)
        self.memory['values'].append(value)
        self.memory['log_probs'].append(log_prob)
        self.memory['dones'].append(done)
        
    def compute_gae(self, next_value):
        """计算广义优势估计(GAE)"""
        rewards = self.memory['rewards']
        values = self.memory['values']
        dones = self.memory['dones']
        
        advantages = []
        gae = 0
        
        # 从后往前计算GAE
        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[i]
                next_value_i = next_value
            else:
                next_non_terminal = 1.0 - dones[i]
                next_value_i = values[i + 1]
                
            delta = rewards[i] + self.gamma * next_value_i * next_non_terminal - values[i]
            gae = delta + self.gamma * self.gae_lambda * next_non_terminal * gae
            advantages.insert(0, gae)
            
        # 计算回报
        returns = []
        for i in range(len(advantages)):
            returns.append(advantages[i] + values[i])
            
        return advantages, returns
        
    def update_policy(self):
        """更新策略网络"""
        if len(self.memory['states']) == 0:
            return
            
        # 计算最后一个状态的价值（用于GAE计算）
        if self.memory['dones'][-1]:
            next_value = 0.0
        else:
            last_state = self.memory['states'][-1]
            state_tensor = torch.FloatTensor(last_state).unsqueeze(0).to(self.device)
            with torch.no_grad():
                _, next_value = self.network(state_tensor)
                next_value = next_value.item()
                
        # 计算优势和回报
        advantages, returns = self.compute_gae(next_value)
        
        # 转换为张量
        states = torch.FloatTensor(self.memory['states']).to(self.device)
        actions = torch.LongTensor(self.memory['actions']).to(self.device)
        old_log_probs = torch.FloatTensor(self.memory['log_probs']).to(self.device)
        advantages = torch.FloatTensor(advantages).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        
        # 标准化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # PPO更新
        for _ in range(self.ppo_epochs):
            # 获取当前策略的输出
            action_probs, values = self.network(states)
            dist = Categorical(action_probs)
            new_log_probs = dist.log_prob(actions)
            entropy = dist.entropy().mean()
            
            # 计算比率
            ratio = torch.exp(new_log_probs - old_log_probs)
            
            # 计算策略损失
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 1.0 - self.clip_epsilon, 1.0 + self.clip_epsilon) * advantages
            policy_loss = -torch.min(surr1, surr2).mean()
            
            # 计算价值损失
            value_loss = F.mse_loss(values.squeeze(), returns)
            
            # 总损失
            total_loss = (policy_loss + 
                         self.value_coef * value_loss - 
                         self.entropy_coef * entropy)
            
            # 反向传播
            self.optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.network.parameters(), self.max_grad_norm)
            self.optimizer.step()
            
            # 记录损失
            self.policy_losses.append(policy_loss.item())
            self.value_losses.append(value_loss.item())
            self.entropy_losses.append(entropy.item())
            
        # 清空经验缓冲区
        for key in self.memory:
            self.memory[key].clear()
            
    def train(self, max_episodes=1000, max_steps_per_episode=500, update_frequency=128):
        """训练PPO智能体"""
        print(f"开始训练PPO智能体，最大回合数: {max_episodes}")
        
        step_count = 0
        
        for episode in range(max_episodes):
            current_pos = self.start
            state = self.preprocess_state(current_pos)
            total_reward = 0
            steps = 0
            
            for step in range(max_steps_per_episode):
                # 获取动作和价值
                action, log_prob, value = self.get_action_and_value(state)
                
                # 执行动作
                next_pos, reward, done = self.step(current_pos, action)
                next_state = self.preprocess_state(next_pos)
                
                # 存储经验
                self.store_experience(state, action, reward, value, log_prob, done)
                
                # 更新状态
                state = next_state
                current_pos = next_pos
                total_reward += reward
                steps += 1
                step_count += 1
                
                # 定期更新策略
                if step_count % update_frequency == 0:
                    self.update_policy()
                
                if done:
                    break
                    
            self.episode_rewards.append(total_reward)
            self.episode_lengths.append(steps)
            
            # 打印训练进度
            if episode % 100 == 0:
                avg_reward = np.mean(self.episode_rewards[-100:])
                avg_length = np.mean(self.episode_lengths[-100:])
                print(f"回合 {episode}, 平均奖励: {avg_reward:.2f}, 平均步数: {avg_length:.1f}")
                
        # 最后更新一次
        if len(self.memory['states']) > 0:
            self.update_policy()
            
        print("PPO训练完成!")
        
    def plan_path(self, max_steps=1000):
        """使用训练好的网络规划路径"""
        path = [self.start]
        current_pos = self.start
        
        for _ in range(max_steps):
            state = self.preprocess_state(current_pos)
            action, _, _ = self.get_action_and_value(state)
            
            next_pos, _, done = self.step(current_pos, action)
            path.append(next_pos)
            current_pos = next_pos
            
            if done:
                print(f"PPO找到路径，长度: {len(path)}")
                return path
                
            # 避免无限循环
            if len(path) > 3 and path[-1] == path[-3]:
                break
                
        print("PPO未能找到路径")
        return None

    def visualize_training(self):
        """可视化训练过程"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        # 奖励曲线
        ax1.plot(self.episode_rewards)
        ax1.set_title('PPO训练奖励')
        ax1.set_xlabel('回合')
        ax1.set_ylabel('总奖励')
        ax1.grid(True)

        # 回合长度
        ax2.plot(self.episode_lengths)
        ax2.set_title('PPO回合长度')
        ax2.set_xlabel('回合')
        ax2.set_ylabel('步数')
        ax2.grid(True)

        # 策略损失
        if self.policy_losses:
            ax3.plot(self.policy_losses, label='Policy Loss')
            ax3.plot(self.value_losses, label='Value Loss')
            ax3.plot(self.entropy_losses, label='Entropy Loss')
            ax3.set_title('PPO损失曲线')
            ax3.set_xlabel('更新次数')
            ax3.set_ylabel('损失')
            ax3.legend()
            ax3.grid(True)

        # 移动平均奖励
        if len(self.episode_rewards) > 10:
            window = min(100, len(self.episode_rewards) // 10)
            moving_avg = np.convolve(self.episode_rewards,
                                   np.ones(window)/window, mode='valid')
            ax4.plot(moving_avg)
            ax4.set_title(f'PPO移动平均奖励 (窗口={window})')
            ax4.set_xlabel('回合')
            ax4.set_ylabel('平均奖励')
            ax4.grid(True)

        plt.tight_layout()
        plt.show()

    def save_model(self, filepath):
        """保存模型"""
        torch.save({
            'network_state_dict': self.network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'policy_losses': self.policy_losses,
            'value_losses': self.value_losses,
            'entropy_losses': self.entropy_losses
        }, filepath)
        print(f"PPO模型已保存到: {filepath}")

    def load_model(self, filepath):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.network.load_state_dict(checkpoint['network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.episode_rewards = checkpoint['episode_rewards']
        self.episode_lengths = checkpoint['episode_lengths']
        self.policy_losses = checkpoint['policy_losses']
        self.value_losses = checkpoint['value_losses']
        self.entropy_losses = checkpoint['entropy_losses']
        print(f"PPO模型已从 {filepath} 加载")

class A3CPathPlanner:
    """基于A3C的异步路径规划器（简化版）"""
    def __init__(self, start, goal, grid_map, learning_rate=1e-4):
        self.start = start
        self.goal = goal
        self.grid_map = np.array(grid_map)
        self.map_shape = self.grid_map.shape

        # 动作空间
        self.actions = [(-1,-1), (-1,0), (-1,1), (0,-1), (0,1), (1,-1), (1,0), (1,1)]
        self.action_size = len(self.actions)

        # 设备选择
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 网络初始化
        self.network = ActorCriticNetwork(
            input_channels=3,
            action_size=self.action_size
        ).to(self.device)

        self.optimizer = optim.Adam(self.network.parameters(), lr=learning_rate)

        # 训练统计
        self.episode_rewards = []
        self.episode_lengths = []

    def preprocess_state(self, position):
        """预处理状态"""
        window_size = 84
        half_window = window_size // 2

        state = np.zeros((3, window_size, window_size), dtype=np.float32)

        start_x = max(0, position[0] - half_window)
        end_x = min(self.map_shape[0], position[0] + half_window)
        start_y = max(0, position[1] - half_window)
        end_y = min(self.map_shape[1], position[1] + half_window)

        state_start_x = half_window - (position[0] - start_x)
        state_end_x = state_start_x + (end_x - start_x)
        state_start_y = half_window - (position[1] - start_y)
        state_end_y = state_start_y + (end_y - start_y)

        # 障碍物信息
        state[0, state_start_x:state_end_x, state_start_y:state_end_y] = \
            self.grid_map[start_x:end_x, start_y:end_y]

        # 目标位置信息
        goal_x = self.goal[0] - position[0] + half_window
        goal_y = self.goal[1] - position[1] + half_window
        if 0 <= goal_x < window_size and 0 <= goal_y < window_size:
            state[1, goal_x, goal_y] = 1.0

        # 当前位置信息
        state[2, half_window, half_window] = 1.0

        return state

    def get_action_and_value(self, state):
        """获取动作和价值"""
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        action_probs, state_value = self.network(state_tensor)

        dist = Categorical(action_probs)
        action = dist.sample()
        log_prob = dist.log_prob(action)

        return action.item(), log_prob, state_value.squeeze()

    def is_valid_position(self, pos):
        """检查位置是否有效"""
        x, y = pos
        if x < 0 or x >= self.map_shape[0] or y < 0 or y >= self.map_shape[1]:
            return False
        return self.grid_map[x, y] == 0

    def get_reward(self, current_pos, next_pos, done):
        """计算奖励函数"""
        reward = -0.01

        if done and next_pos == self.goal:
            reward += 100.0

        if not self.is_valid_position(next_pos):
            reward -= 10.0

        current_dist = np.linalg.norm(np.array(current_pos) - np.array(self.goal))
        next_dist = np.linalg.norm(np.array(next_pos) - np.array(self.goal))
        reward += (current_dist - next_dist) * 0.1

        return reward

    def step(self, current_pos, action):
        """执行动作"""
        dx, dy = self.actions[action]
        next_pos = (current_pos[0] + dx, current_pos[1] + dy)

        done = (next_pos == self.goal)

        if not self.is_valid_position(next_pos):
            next_pos = current_pos

        reward = self.get_reward(current_pos, next_pos, done)

        return next_pos, reward, done

    def train(self, max_episodes=500, max_steps_per_episode=300):
        """训练A3C智能体"""
        print(f"开始训练A3C智能体，最大回合数: {max_episodes}")

        for episode in range(max_episodes):
            current_pos = self.start
            state = self.preprocess_state(current_pos)
            total_reward = 0
            steps = 0

            log_probs = []
            values = []
            rewards = []

            for step in range(max_steps_per_episode):
                action, log_prob, value = self.get_action_and_value(state)

                next_pos, reward, done = self.step(current_pos, action)
                next_state = self.preprocess_state(next_pos)

                log_probs.append(log_prob)
                values.append(value)
                rewards.append(reward)

                state = next_state
                current_pos = next_pos
                total_reward += reward
                steps += 1

                if done:
                    break

            # 计算回报
            returns = []
            R = 0
            for r in reversed(rewards):
                R = r + 0.99 * R
                returns.insert(0, R)

            returns = torch.FloatTensor(returns).to(self.device)
            log_probs = torch.stack(log_probs)
            values = torch.stack(values)

            # 计算优势
            advantages = returns - values

            # 计算损失
            policy_loss = -(log_probs * advantages.detach()).mean()
            value_loss = advantages.pow(2).mean()

            total_loss = policy_loss + 0.5 * value_loss

            # 更新网络
            self.optimizer.zero_grad()
            total_loss.backward()
            self.optimizer.step()

            self.episode_rewards.append(total_reward)
            self.episode_lengths.append(steps)

            if episode % 100 == 0:
                avg_reward = np.mean(self.episode_rewards[-100:])
                print(f"A3C回合 {episode}, 平均奖励: {avg_reward:.2f}")

        print("A3C训练完成!")

    def plan_path(self, max_steps=1000):
        """规划路径"""
        path = [self.start]
        current_pos = self.start

        for _ in range(max_steps):
            state = self.preprocess_state(current_pos)
            action, _, _ = self.get_action_and_value(state)

            next_pos, _, done = self.step(current_pos, action)
            path.append(next_pos)
            current_pos = next_pos

            if done:
                print(f"A3C找到路径，长度: {len(path)}")
                return path

            if len(path) > 3 and path[-1] == path[-3]:
                break

        print("A3C未能找到路径")
        return None

    def visualize_training(self):
        """可视化训练过程"""
        plt.figure(figsize=(12, 4))

        plt.subplot(1, 2, 1)
        plt.plot(self.episode_rewards)
        plt.title('A3C训练奖励')
        plt.xlabel('回合')
        plt.ylabel('总奖励')
        plt.grid(True)

        plt.subplot(1, 2, 2)
        plt.plot(self.episode_lengths)
        plt.title('A3C回合长度')
        plt.xlabel('回合')
        plt.ylabel('步数')
        plt.grid(True)

        plt.tight_layout()
        plt.show()
