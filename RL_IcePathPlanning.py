import numpy as np
import matplotlib.pyplot as plt
import random
from collections import defaultdict

class IceQLearningPathPlanner:
    """
    专门为海冰路径规划设计的Q-Learning算法
    考虑海冰环境的特殊性：复杂地形、多目标优化等
    """
    def __init__(self, start, goal, ice_map, risk_map1=None, risk_map2=None,
                 learning_rate=0.1, discount_factor=0.9, epsilon=0.1, 
                 epsilon_decay=0.995, min_epsilon=0.01, max_episodes=1000):
        """
        初始化海冰Q-Learning路径规划器
        
        Args:
            start: 起点坐标 (x, y)
            goal: 终点坐标 (x, y)
            ice_map: 海冰地图，0表示自由水域，非0表示海冰
            risk_map1: 风险地图1（可选）
            risk_map2: 风险地图2（可选）
            learning_rate: 学习率
            discount_factor: 折扣因子
            epsilon: 探索率
            epsilon_decay: 探索率衰减
            min_epsilon: 最小探索率
            max_episodes: 最大训练回合数
        """
        self.start = start
        self.goal = goal
        self.ice_map = np.array(ice_map)
        self.map_size = self.ice_map.shape
        
        # 风险地图（如果提供）
        self.risk_map1 = risk_map1 if risk_map1 is not None else np.zeros_like(ice_map)
        self.risk_map2 = risk_map2 if risk_map2 is not None else np.zeros_like(ice_map)
        
        # Q-Learning参数
        self.learning_rate = learning_rate
        self.discount_factor = discount_factor
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.min_epsilon = min_epsilon
        self.max_episodes = max_episodes
        
        # 动作空间：8个方向
        self.actions = [(0, 1), (1, 0), (0, -1), (-1, 0),
                       (1, 1), (-1, 1), (-1, -1), (1, -1)]
        self.num_actions = len(self.actions)
        
        # Q表
        self.q_table = defaultdict(lambda: np.zeros(self.num_actions))
        
        # 训练统计
        self.episode_rewards = []
        self.episode_lengths = []
        self.success_episodes = []
        self.path_costs = []
        self.path_risks = []
        
    def _is_valid_state(self, state):
        """检查状态是否有效（在自由水域中）"""
        x, y = state
        return (0 <= x < self.map_size[0] and 
                0 <= y < self.map_size[1] and 
                self.ice_map[x, y] == 0)
    
    def _get_next_state(self, state, action_idx):
        """根据动作获取下一个状态"""
        x, y = state
        dx, dy = self.actions[action_idx]
        next_state = (x + dx, y + dy)
        
        if self._is_valid_state(next_state):
            return next_state
        else:
            return state  # 无效动作，保持原状态
    
    def _calculate_ice_risk(self, state):
        """计算海冰风险"""
        x, y = state
        if not (0 <= x < self.map_size[0] and 0 <= y < self.map_size[1]):
            return 1.0  # 边界外高风险
        
        # 综合风险评估
        ice_density = self.ice_map[x, y]
        risk1 = self.risk_map1[x, y] if self.risk_map1 is not None else 0
        risk2 = self.risk_map2[x, y] if self.risk_map2 is not None else 0
        
        # 计算局部海冰密度（3x3邻域）
        local_ice_density = 0
        count = 0
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                nx, ny = x + dx, y + dy
                if 0 <= nx < self.map_size[0] and 0 <= ny < self.map_size[1]:
                    local_ice_density += self.ice_map[nx, ny]
                    count += 1
        local_ice_density = local_ice_density / count if count > 0 else 0
        
        # 综合风险评分
        total_risk = (ice_density * 0.4 + 
                     local_ice_density * 0.3 + 
                     risk1 * 0.15 + 
                     risk2 * 0.15)
        
        return min(total_risk, 1.0)
    
    def _get_reward(self, state, action_idx, next_state):
        """计算多目标奖励函数"""
        # 到达目标的大奖励
        if next_state == self.goal:
            return 100.0
        
        # 撞到海冰或越界的惩罚
        if not self._is_valid_state(next_state) or next_state == state:
            return -20.0
        
        # 基本移动代价
        dx, dy = self.actions[action_idx]
        if abs(dx) + abs(dy) == 2:  # 对角线移动
            base_cost = -1.4
        else:  # 直线移动
            base_cost = -1.0
        
        # 距离目标的启发式奖励
        current_dist = abs(state[0] - self.goal[0]) + abs(state[1] - self.goal[1])
        next_dist = abs(next_state[0] - self.goal[0]) + abs(next_state[1] - self.goal[1])
        distance_reward = (current_dist - next_dist) * 0.5
        
        # 海冰风险惩罚
        ice_risk = self._calculate_ice_risk(next_state)
        risk_penalty = -ice_risk * 5.0
        
        # 路径平滑性奖励（避免急转弯）
        smoothness_reward = 0
        if hasattr(self, 'last_action') and self.last_action is not None:
            if action_idx == self.last_action:
                smoothness_reward = 0.1  # 保持方向的小奖励
        
        total_reward = base_cost + distance_reward + risk_penalty + smoothness_reward
        return total_reward
    
    def _choose_action(self, state, training=True):
        """选择动作（ε-贪婪策略）"""
        if training and random.random() < self.epsilon:
            # 探索：随机选择动作
            return random.randint(0, self.num_actions - 1)
        else:
            # 利用：选择Q值最大的动作
            return np.argmax(self.q_table[state])
    
    def _update_q_table(self, state, action, reward, next_state, done):
        """更新Q表"""
        current_q = self.q_table[state][action]
        
        if done:
            target_q = reward
        else:
            max_next_q = np.max(self.q_table[next_state])
            target_q = reward + self.discount_factor * max_next_q
        
        # Q-learning更新规则
        self.q_table[state][action] = current_q + self.learning_rate * (target_q - current_q)
    
    def train(self):
        """训练Q-learning智能体"""
        print("开始海冰Q-Learning训练...")
        
        for episode in range(self.max_episodes):
            state = self.start
            total_reward = 0
            steps = 0
            path_cost = 0
            path_risk = 0
            max_steps = self.map_size[0] * self.map_size[1] * 2
            
            self.last_action = None  # 重置上一个动作
            
            while state != self.goal and steps < max_steps:
                # 选择动作
                action = self._choose_action(state, training=True)
                
                # 执行动作
                next_state = self._get_next_state(state, action)
                reward = self._get_reward(state, action, next_state)
                done = (next_state == self.goal)
                
                # 更新Q表
                self._update_q_table(state, action, reward, next_state, done)
                
                # 累计统计
                state = next_state
                total_reward += reward
                steps += 1
                
                # 计算路径代价和风险
                if next_state != state:  # 有效移动
                    dx, dy = self.actions[action]
                    step_cost = 1.4 if abs(dx) + abs(dy) == 2 else 1.0
                    path_cost += step_cost
                    path_risk += self._calculate_ice_risk(next_state)
                
                self.last_action = action
            
            # 记录训练统计
            self.episode_rewards.append(total_reward)
            self.episode_lengths.append(steps)
            self.success_episodes.append(state == self.goal)
            self.path_costs.append(path_cost)
            self.path_risks.append(path_risk)
            
            # 衰减探索率
            if self.epsilon > self.min_epsilon:
                self.epsilon *= self.epsilon_decay
            
            # 打印进度
            if episode % 100 == 0:
                recent_success_rate = np.mean(self.success_episodes[-100:]) if len(self.success_episodes) >= 100 else np.mean(self.success_episodes)
                avg_cost = np.mean(self.path_costs[-100:]) if len(self.path_costs) >= 100 else np.mean(self.path_costs)
                avg_risk = np.mean(self.path_risks[-100:]) if len(self.path_risks) >= 100 else np.mean(self.path_risks)
                print(f"Episode {episode}: Success Rate = {recent_success_rate:.2f}, "
                      f"Avg Cost = {avg_cost:.2f}, Avg Risk = {avg_risk:.3f}, Epsilon = {self.epsilon:.3f}")
        
        print("训练完成!")
    
    def plan_path(self):
        """使用训练好的策略规划路径"""
        path = []
        state = self.start
        max_steps = self.map_size[0] * self.map_size[1]
        
        while state != self.goal and len(path) < max_steps:
            path.append(state)
            action = self._choose_action(state, training=False)
            state = self._get_next_state(state, action)
            
            # 防止无限循环
            if state in path:
                break
        
        if state == self.goal:
            path.append(state)
            return path
        else:
            return None
    
    def evaluate_path(self, path):
        """评估路径的多个指标"""
        if not path:
            return None
        
        # 路径长度
        length = len(path)
        
        # 路径代价（考虑对角线移动）
        cost = 0
        for i in range(len(path) - 1):
            dx = abs(path[i+1][0] - path[i][0])
            dy = abs(path[i+1][1] - path[i][1])
            if dx + dy == 2:  # 对角线
                cost += 1.4
            else:  # 直线
                cost += 1.0
        
        # 总风险
        total_risk = sum(self._calculate_ice_risk(pos) for pos in path)
        avg_risk = total_risk / length
        
        # 平滑性（转弯次数）
        turns = 0
        if len(path) > 2:
            for i in range(1, len(path) - 1):
                prev_dir = (path[i][0] - path[i-1][0], path[i][1] - path[i-1][1])
                next_dir = (path[i+1][0] - path[i][0], path[i+1][1] - path[i][1])
                if prev_dir != next_dir:
                    turns += 1
        
        return {
            'length': length,
            'cost': cost,
            'total_risk': total_risk,
            'avg_risk': avg_risk,
            'turns': turns,
            'smoothness': 1.0 / (1.0 + turns)  # 平滑性指标
        }
    
    def visualize_training(self):
        """可视化训练过程"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # 奖励曲线
        axes[0, 0].plot(self.episode_rewards)
        axes[0, 0].set_title('Episode Rewards')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Total Reward')
        
        # 路径代价曲线
        axes[0, 1].plot(self.path_costs)
        axes[0, 1].set_title('Path Costs')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Path Cost')
        
        # 路径风险曲线
        axes[0, 2].plot(self.path_risks)
        axes[0, 2].set_title('Path Risks')
        axes[0, 2].set_xlabel('Episode')
        axes[0, 2].set_ylabel('Total Risk')
        
        # 成功率曲线
        window_size = 100
        if len(self.success_episodes) >= window_size:
            success_rate = [np.mean(self.success_episodes[i:i+window_size]) 
                           for i in range(len(self.success_episodes) - window_size + 1)]
            axes[1, 0].plot(success_rate)
        axes[1, 0].set_title(f'Success Rate (Window={window_size})')
        axes[1, 0].set_xlabel('Episode')
        axes[1, 0].set_ylabel('Success Rate')
        
        # 步数曲线
        axes[1, 1].plot(self.episode_lengths)
        axes[1, 1].set_title('Episode Lengths')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].set_ylabel('Steps')
        
        # Q值热图
        q_values = np.zeros(self.map_size)
        for (x, y), q_vals in self.q_table.items():
            if 0 <= x < self.map_size[0] and 0 <= y < self.map_size[1]:
                q_values[x, y] = np.max(q_vals)
        
        im = axes[1, 2].imshow(q_values.T, cmap='viridis', origin='lower')
        axes[1, 2].set_title('Max Q-Values')
        plt.colorbar(im, ax=axes[1, 2])
        
        plt.tight_layout()
        plt.show()
    
    def visualize_path(self, path=None, show_risk=True):
        """可视化路径和环境"""
        if path is None:
            path = self.plan_path()
        
        fig, axes = plt.subplots(1, 2, figsize=(16, 8))
        
        # 左图：路径和Q值
        axes[0].imshow(self.ice_map, cmap='gray_r', alpha=0.7)
        
        # 绘制Q值作为背景
        q_values = np.zeros(self.map_size)
        for (x, y), q_vals in self.q_table.items():
            if 0 <= x < self.map_size[0] and 0 <= y < self.map_size[1]:
                q_values[x, y] = np.max(q_vals)
        
        axes[0].imshow(q_values.T, cmap='Blues', alpha=0.3, origin='lower')
        
        # 绘制路径
        if path:
            path_array = np.array(path)
            axes[0].plot(path_array[:, 0], path_array[:, 1], 'g-', linewidth=2, label='RL Path')
            axes[0].plot(path_array[:, 0], path_array[:, 1], 'go', markersize=3)
        
        axes[0].plot(self.start[0], self.start[1], 'go', markersize=10, label='Start')
        axes[0].plot(self.goal[0], self.goal[1], 'ro', markersize=10, label='Goal')
        axes[0].set_title('Q-Learning Path with Q-Values')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 右图：风险分布
        if show_risk and (self.risk_map1 is not None or self.risk_map2 is not None):
            # 计算综合风险地图
            risk_map = np.zeros(self.map_size)
            for x in range(self.map_size[0]):
                for y in range(self.map_size[1]):
                    risk_map[x, y] = self._calculate_ice_risk((x, y))
            
            im = axes[1].imshow(risk_map.T, cmap='Reds', alpha=0.7, origin='lower')
            axes[1].imshow(self.ice_map, cmap='gray_r', alpha=0.3)
            
            # 绘制路径
            if path:
                path_array = np.array(path)
                axes[1].plot(path_array[:, 0], path_array[:, 1], 'b-', linewidth=2, label='RL Path')
            
            axes[1].plot(self.start[0], self.start[1], 'go', markersize=10, label='Start')
            axes[1].plot(self.goal[0], self.goal[1], 'ro', markersize=10, label='Goal')
            axes[1].set_title('Risk Distribution')
            axes[1].legend()
            plt.colorbar(im, ax=axes[1])
        else:
            axes[1].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # 打印路径评估
        if path:
            metrics = self.evaluate_path(path)
            print(f"\n路径评估:")
            print(f"  长度: {metrics['length']} 步")
            print(f"  代价: {metrics['cost']:.2f}")
            print(f"  总风险: {metrics['total_risk']:.3f}")
            print(f"  平均风险: {metrics['avg_risk']:.3f}")
            print(f"  转弯次数: {metrics['turns']}")
            print(f"  平滑性: {metrics['smoothness']:.3f}")
        
        return path

def main():
    """测试海冰Q-Learning路径规划"""
    # 创建海冰测试地图
    ice_map = np.array([
        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
        [0, 1, 1, 0, 0, 0, 0, 1, 1, 0],
        [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
        [0, 0, 0, 0, 1, 1, 0, 0, 0, 0],
        [0, 0, 1, 0, 0, 0, 0, 1, 0, 0],
        [0, 0, 1, 0, 0, 0, 0, 1, 0, 0],
        [0, 0, 0, 0, 0, 1, 0, 0, 0, 0],
        [0, 1, 1, 0, 0, 1, 0, 1, 1, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    
    # 创建风险地图
    risk_map1 = np.random.rand(10, 10) * 0.3  # 随机风险
    risk_map2 = np.random.rand(10, 10) * 0.2  # 另一种风险
    
    start = (0, 0)
    goal = (9, 9)
    
    # 创建海冰Q-Learning规划器
    planner = IceQLearningPathPlanner(start, goal, ice_map, risk_map1, risk_map2, max_episodes=1000)
    
    # 训练
    planner.train()
    
    # 可视化训练过程
    planner.visualize_training()
    
    # 规划路径并可视化
    path = planner.visualize_path()
    
    if path:
        print(f"找到路径，长度: {len(path)}")
    else:
        print("未找到路径")

if __name__ == "__main__":
    main()
