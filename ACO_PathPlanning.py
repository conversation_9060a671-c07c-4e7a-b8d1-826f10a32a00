import numpy as np
import matplotlib.pyplot as plt

class ACOPathPlanner:
    def __init__(self, start, goal, grid_map, n_ants=50, max_iter=100, alpha=1.0, beta=2.0, rho=0.1, Q=1.0):
        self.start = np.array(start)
        self.goal = np.array(goal)
        self.grid_map = np.array(grid_map)
        self.map_size = self.grid_map.shape  # 修复：使用shape获取正确尺寸
        self.n_ants = n_ants
        self.max_iter = max_iter
        self.alpha = alpha
        self.beta = beta
        self.rho = rho
        self.Q = Q

        # 初始化信息素矩阵
        self.pheromone = np.ones(self.map_size) * 0.1
        self.best_path = None
        self.best_length = float('inf')
        
    def _check_collision(self, point):
        """检查点是否与障碍物碰撞"""
        x, y = int(point[0]), int(point[1])
        if 0 <= x < self.map_size[0] and 0 <= y < self.map_size[1]:
            return self.grid_map[x, y] != 0  # 非0值表示障碍物
        return True  # 超出边界视为碰撞
    
    def _get_neighbors(self, current):
        # 获取当前位置的8个相邻网格点
        neighbors = []
        moves = [(0, 1), (1, 0), (0, -1), (-1, 0),
                (1, 1), (-1, 1), (-1, -1), (1, -1)]
        
        for dx, dy in moves:
            next_x = current[0] + dx
            next_y = current[1] + dy
            
            if (0 <= next_x < self.map_size[0] and 
                0 <= next_y < self.map_size[1] and
                not self._check_collision((next_x, next_y))):
                neighbors.append(np.array([next_x, next_y]))
        
        return neighbors
    
    def _heuristic(self, pos):
        """计算启发式信息（距离目标越近值越大）"""
        manhattan_dist = np.abs(self.goal[0] - pos[0]) + np.abs(self.goal[1] - pos[1])
        if manhattan_dist == 0:
            return 1.0  # 已到达目标
        return 1.0 / manhattan_dist  # 距离越近，启发值越大
    
    def _construct_solution(self):
        """构建一只蚂蚁的解决方案"""
        current = self.start.copy()
        path = [current.copy()]
        visited = set()  # 防止循环
        visited.add(tuple(current))

        max_steps = self.map_size[0] * self.map_size[1]  # 最大步数限制

        while not np.array_equal(current, self.goal) and len(path) < max_steps:
            neighbors = self._get_neighbors(current)
            if not neighbors:
                return None, float('inf')

            # 过滤掉已访问的邻居（防止循环）
            unvisited_neighbors = [n for n in neighbors if tuple(n) not in visited]
            if not unvisited_neighbors:
                # 如果所有邻居都访问过，随机选择一个
                unvisited_neighbors = neighbors

            # 计算转移概率
            pheromone_values = [self.pheromone[int(next_pos[0]), int(next_pos[1])] for next_pos in unvisited_neighbors]
            heuristic_values = [self._heuristic(next_pos) for next_pos in unvisited_neighbors]

            probabilities = []
            for ph, h in zip(pheromone_values, heuristic_values):
                probabilities.append((ph ** self.alpha) * (h ** self.beta))

            probabilities = np.array(probabilities)
            if probabilities.sum() == 0:
                next_pos = unvisited_neighbors[np.random.randint(len(unvisited_neighbors))]
            else:
                probabilities = probabilities / probabilities.sum()
                next_pos = unvisited_neighbors[np.random.choice(len(unvisited_neighbors), p=probabilities)]

            current = next_pos
            path.append(current.copy())
            visited.add(tuple(current))

        if np.array_equal(current, self.goal):
            return path, self._calculate_path_length(path)
        else:
            return None, float('inf')
    
    def _calculate_path_length(self, path):
        if path is None:
            return float('inf')
        
        length = 0
        for i in range(len(path) - 1):
            length += np.linalg.norm(path[i+1] - path[i])
        return length
    
    def _update_pheromone(self, paths, lengths):
        # 信息素蒸发
        self.pheromone *= (1 - self.rho)
        
        # 信息素沉积
        for path, length in zip(paths, lengths):
            if path is not None:
                delta = self.Q / length
                for pos in path:
                    self.pheromone[int(pos[0]), int(pos[1])] += delta
    
    def optimize(self):
        for iteration in range(self.max_iter):
            paths = []
            lengths = []
            
            # 构建解决方案
            for _ in range(self.n_ants):
                path, length = self._construct_solution()
                paths.append(path)
                lengths.append(length)
                
                if length < self.best_length:
                    self.best_length = length
                    self.best_path = path
            
            # 更新信息素
            self._update_pheromone(paths, lengths)
            
            if iteration % 10 == 0:
                print(f"迭代 {iteration}, 当前最优路径长度: {self.best_length}")
        
        return self.best_path

def main():
    # 示例网格地图
    grid_map = [
        [0, 0, 0, 0, 0],
        [0, 1, 1, 1, 0],
        [0, 0, 0, 1, 1],
        [0, 1, 0, 0, 0],
        [0, 0, 0, 0, 0]
    ]
    
    start = (0, 0)
    goal = (4, 4)
    
    # 创建ACO路径规划器实例
    aco_planner = ACOPathPlanner(start, goal, grid_map)
    
    # 运行优化
    best_path = aco_planner.optimize()
    
    if best_path is not None:
        # 可视化结果
        plt.figure(figsize=(8, 8))

        # 绘制地图（障碍物为黑色）
        plt.imshow(aco_planner.grid_map, cmap='gray_r', alpha=0.7)

        # 绘制信息素分布
        plt.imshow(aco_planner.pheromone.T, cmap='Blues', alpha=0.5)

        # 绘制最优路径
        path_array = np.array(best_path)
        plt.plot(path_array[:, 0], path_array[:, 1], 'g-', linewidth=2, label='Path')
        plt.plot(start[0], start[1], 'go', markersize=8, label='Start')
        plt.plot(goal[0], goal[1], 'ro', markersize=8, label='Goal')

        plt.grid(True)
        plt.legend()
        plt.axis('equal')
        plt.title(f'ACO 路径规划 - 长度: {aco_planner.best_length:.2f}')
        plt.show()
    else:
        print("未找到可行路径")
        # 展示失败时的信息素分布
        plt.figure(figsize=(8, 8))

        # 绘制地图
        plt.imshow(aco_planner.grid_map, cmap='gray_r', alpha=0.7)

        # 绘制信息素分布
        plt.imshow(aco_planner.pheromone.T, cmap='Blues', alpha=0.5)

        plt.plot(start[0], start[1], 'go', markersize=8, label='Start')
        plt.plot(goal[0], goal[1], 'ro', markersize=8, label='Goal')

        plt.grid(True)
        plt.legend()
        plt.axis('equal')
        plt.title('ACO 信息素分布（未找到路径）')
        plt.show()

if __name__ == "__main__":
    main()