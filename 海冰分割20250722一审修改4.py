import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from mpl_toolkits.mplot3d import Axes3D
from skimage.segmentation import random_walker
from skimage.filters import gaussian, threshold_otsu,difference_of_gaussians
from skimage.measure import label, regionprops_table
import pandas as pd
from skimage.measure import regionprops
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler
from scipy.ndimage import binary_dilation
import matplotlib.pyplot as plt
import math
import numpy as np
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage.filters import gaussian
from scipy.ndimage import binary_dilation
from skimage.feature import canny, local_binary_pattern
from skimage.filters import sobel
from scipy.ndimage import gaussian_filter
import os
from scipy import ndimage
import matplotlib.patches as patches
import random
from bisect import bisect_left
from copy import deepcopy
import sys
import os

# 添加python_motion_planning-master到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'python_motion_planning-master', 'src'))

# 配置matplotlib以改善图像显示
plt.rcParams['figure.max_open_warning'] = 50
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['image.cmap'] = 'gray'
plt.rcParams['image.interpolation'] = 'nearest'

def safe_imshow(img, ax=None, **kwargs):
    """安全的图像显示函数，处理各种数据类型和范围"""
    if ax is None:
        ax = plt.gca()

    # 处理不同的数据类型
    if img.dtype == np.bool_:
        img_display = img.astype(np.uint8) * 255
    elif img.dtype == np.float32 or img.dtype == np.float64:
        if img.max() <= 1.0:
            img_display = (img * 255).astype(np.uint8)
        else:
            img_display = np.clip(img, 0, 255).astype(np.uint8)
    else:
        img_display = np.clip(img, 0, 255).astype(np.uint8)

    # 设置默认参数
    default_kwargs = {'cmap': 'gray', 'vmin': 0, 'vmax': 255}
    default_kwargs.update(kwargs)

    return ax.imshow(img_display, **default_kwargs)
import time
import cmasher as cmr
import matplotlib.patches as patches
from scipy import ndimage
from scipy.spatial import ConvexHull
from matplotlib.path import Path
# ==================== 忽略警告 ====================
import warnings
warnings.filterwarnings('ignore')
# ==================== 图片颜色的定义 ====================
cmapv = cmr.get_sub_cmap('cmr.fusion_r', 0, 1, N=32)#
cmapp = cmr.get_sub_cmap('cmr.copper', 0, 1,)#N=32
cmapw = cmr.get_sub_cmap('cmr.viola', 0, 1,)#N=32
cmapwater = cmr.get_sub_cmap('cmr.ocean', 0, 1,)#N=32
cmap = cmr.get_sub_cmap('cmr.seasons', 0, 1,)#N=32
# ===================== Step 0: 读取图像 =====================
# 读取输入图像
image_path = "D:/代码/ICE/sea_ice/sea_ice_078.jpg"
image_path = "D:/代码/ICE/sea_ice/sea_ice_419.jpg"

# image_path = "C:/Users/<USER>/Desktop/EAAI一审/图片/卫星遥感图片.png"#遥感图片
# image_path = "C:/Users/<USER>/Desktop/EAAI一审/图片/无人机图片.png"#遥感图片
# image_path = "C:/Users/<USER>/Desktop/EAAI一审/图片/实验室相机图片1.jpg"#遥感图片
# image_path = "C:/Users/<USER>/Desktop/EAAI一审/图片/实验室船载相机图片2.jpg"#遥感图片
# image_path = "C:/Users/<USER>/Desktop/EAAI一审/图片/船载图片1.jpg"#遥感图片
# image_path = "C:/Users/<USER>/Desktop/EAAI一审/图片/船载图片2.jpg"#遥感图片
# image_path = "C:/Users/<USER>/Desktop/EAAI一审/图片/船载图片3.jpg"#遥感图片

# 检查图像路径是否存在
if not os.path.exists(image_path):
    raise FileNotFoundError(f"图像路径 '{image_path}' 不存在，请检查路径是否正确！")
# 读取输入图像
image = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_COLOR)
# 检查图像是否成功读取
if image is None:
    raise ValueError(f"无法读取图像 '{image_path}'，请检查文件是否损坏或格式是否支持！")
######=====================A.图像预处理 =====================#######
# ===================== Step 1: 转为灰度图像 =====================
# 转换为灰度图像，简化计算，突出亮度差异，适用于大多数分割算法
gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
# ===================== Step 2: 图像平滑处理（滤波降噪） =====================
# 高斯滤波平滑图像，减少高频噪声，保持边缘信息
#smoothed_image = gaussian(gray_image, sigma=0.4)
# 自适应非局部均值滤波（Non-Local Means Denoising, NL-Means）
smoothed_image = cv2.fastNlMeansDenoising(gray_image, h=10, templateWindowSize=5, searchWindowSize=20)
# 中值滤波进一步去除椒盐噪声
#smoothed_image = cv2.medianBlur((gray_image * 255).astype(np.uint8), 5)
# ===================== Step 2: 减少反光 =====================
#光照反光抑制与对比度增强：  为了减轻反光干扰，并增强冰水之间的视觉差异
#同态滤波 (Homomorphic Filtering)： 可以将图像的照度分量和反射分量分离，
#减弱照度分量的影响，从而减少光照不均匀和反光现象。
def homomorphic_filter(img, gamma_h=1.3, gamma_l=0.5, d0=10, c=10):
    """
    同态滤波器
    Args:
        img: 灰度图像
        gamma_h: 高频增益
        gamma_l: 低频增益
        d0: 截止频率
        c: 锐化系数
    Returns:
        滤波后的图像
    """
    rows, cols = img.shape
    img_log = np.log1p(np.array(img, dtype="float") / 255) # 转换为对数域
    # 构建滤波器
    H = np.zeros((rows, cols), np.float32)
    for i in range(rows):
        for j in range(cols):
            H[i, j] = (gamma_h - gamma_l) * (1 - np.exp(-c * ((i**2 + j**2) / d0**2))) + gamma_l
    # 频域变换
    img_fft = np.fft.fft2(img_log)
    img_fft_shifted = np.fft.fftshift(img_fft)
    # 滤波
    img_fft_filtered = img_fft_shifted * H
    img_fft_inverse_shifted = np.fft.ifftshift(img_fft_filtered)
    img_filtered = np.fft.ifft2(img_fft_inverse_shifted)
    # 指数变换，恢复到图像灰度范围
    img_exp = np.expm1(np.real(img_filtered))
    img_output = np.array(np.clip(img_exp * 255 + 0.5, 0, 255), dtype=np.uint8)
    return img_output
homo_image = homomorphic_filter(smoothed_image) # 对增强对比度后的图像进行同态滤波
# ===================== Step 3: 增强对比度 =====================
# 使用直方图均衡化增强对比度，突出图像细节
#enhanced_image = cv2.equalizeHist(homo_image)
# 自适应直方图均衡化（CLAHE），避免过度增强，适用于局部对比度提升
clahe = cv2.createCLAHE(clipLimit=1, tileGridSize=(10, 10))
enhanced_image = clahe.apply(homo_image)
# ===================== Step 4: 边缘增强与纹理特征提取 =====================
# Canny 边缘检测，提取海冰与水体的边缘特征
#edges = cv2.Canny(enhanced_image, threshold1=50, threshold2=150)
# 使用拉普拉斯滤波器进一步增强边缘，突出细节变化
# laplacian_edges = cv2.Laplacian(enhanced_image, cv2.CV_64F)
# ===================== Step 5: 可视化结果 =====================
# 显示原始图像和处理后的图像
plt.figure(figsize=(8, 8),dpi=1000)
plt.subplot(2, 2, 1)
#plt.title("Original Image")
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.axis('off')
plt.subplot(2, 2, 2)
#plt.title("Grayscale Image")
plt.imshow(gray_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 2, 3)
#plt.title("Smoothed Image")
plt.imshow(smoothed_image, cmap='gray')
plt.axis('off')
plt.subplot(2, 2, 4)
#plt.title("homo_image")
plt.imshow(homo_image, cmap='gray')
plt.axis('off')
# plt.subplot(2, 3, 5)
# plt.title("Contrast Enhanced")
# plt.imshow(enhanced_image, cmap='gray')
# plt.axis('off')
# plt.subplot(2, 3, 6)
# plt.title("Canny Edges")
# plt.imshow(edges, cmap='gray')
# plt.axis('off')
# plt.tight_layout()
plt.show()
######=====================B.图像分割 =====================#######
image_seg =enhanced_image
# ===================== 方法 1: 应用 Otsu 阈值分割 =====================
# 使用 Otsu 方法自动确定阈值进行二值化
_, otsu_thresh = cv2.threshold((image_seg * 255).astype(np.uint8), 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
#===================== 方法 2: K-Means 聚类分割=====================
pixel_values = image_seg.reshape((-1, 1))
pixel_values = np.float32(pixel_values)  # 转为浮点型，便于 K-Means 处理
# 定义 K-Means 聚类参数
criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 100, 0.2)  # 迭代停止条件
k = 2  # 目标分为两类（海冰和水）
_, labels, centers = cv2.kmeans(pixel_values, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
# 将聚类结果映射回图像形状
segmented_image_kmeans = labels.reshape(image_seg.shape)
# ===================== 方法 3: 阈值分割（固定阈值） =====================
#_, fixed_thresh = cv2.threshold(image_seg, 127, 255, cv2.THRESH_BINARY)
# ===================== 方法 4: 自适应均值 =====================
# fixed_thresh = cv2.adaptiveThreshold(image_seg, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,cv2.THRESH_BINARY, 11, 2)
# ===================== 方法 5: 随机游走方法 =====================
# 初始化标记矩阵，0 表示未标记的像素
markers = np.zeros_like(image_seg, dtype=np.int32)
# 手动设置前景（海冰）和背景（水）的标记
markers[image_seg < 100] = 1  # 背景标记
markers[image_seg > 200] = 2  # 前景标记
# 使用随机游走算法对图像进行分割
labels_rw = random_walker(smoothed_image, markers, beta=10, mode='bf')
# ===================== 方法 6: 自适应相场1 =====================
import time
import psutil
import os

# 算法参数
epsilon=0.05
mu=1.0
lambda_=1.5
dt=0.01
n_iters=100
use_adaptive_weights=True
use_edge_preservation=True
use_texture_feature=True

# 开始监测运行时间和内存
print("🚀 Starting APSIS algorithm with performance monitoring...")
start_time = time.time()
process = psutil.Process(os.getpid())
initial_memory = process.memory_info().rss / 1024 / 1024  # MB
peak_memory = initial_memory
# 归一化图像到[0,1]
img = homo_image / 255.0
# 计算边缘特征 - 用于自适应权重和边缘保持正则化项
edge_map = None
texture_feature = None
if use_adaptive_weights or use_edge_preservation:
    # 使用Sobel算子计算梯度幅值作为边缘图
    edge_map = sobel(img)
    edge_map = edge_map / np.max(edge_map)  # 归一化到[0,1]
# 提取纹理特征 - 用于增强分割
if use_texture_feature:
    # 使用不同尺度的高斯滤波器提取纹理特征
    texture_feature = np.zeros_like(img)
    for sigma in [0.5, 1.0, 2.0]:
        texture_feature += np.abs(img - gaussian_filter(img, sigma))
    texture_feature = texture_feature / np.max(texture_feature)  # 归一化
# 初始化相场函数（使用Otsu阈值进行初始化）
_, init_phi = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
phi = init_phi.astype(float) / 255.0

# 相场演化
for i in range(n_iters):
    # 监测内存使用
    current_memory = process.memory_info().rss / 1024 / 1024  # MB
    if current_memory > peak_memory:
        peak_memory = current_memory

    # 每100次迭代输出进度和内存使用情况
    if i % 100 == 0:
        elapsed_time = time.time() - start_time
        print(f"⏱️  Iteration {i}/{n_iters}, Time: {elapsed_time:.2f}s, Memory: {current_memory:.1f}MB")

    # 计算梯度
    grad_phi_x = np.gradient(phi, axis=1)
    grad_phi_y = np.gradient(phi, axis=0)
    grad_phi_norm = np.sqrt(grad_phi_x**2 + grad_phi_y**2)
    # 计算曲率
    laplacian = cv2.Laplacian(phi, cv2.CV_64F)
    # 自适应权重系数 - 创新点1：根据图像局部特征动态调整权重
    lambda_adaptive = lambda_
    mu_adaptive = mu
    epsilon_adaptive = epsilon

    if use_adaptive_weights and edge_map is not None:
        # 在边缘区域减小扩散系数，增大图像驱动力
        edge_weight = 1.0 - 0.7 * edge_map  # 边缘处权重较小
        lambda_adaptive = lambda_ * (1.0 + edge_map)  # 边缘处增大图像驱动力
        mu_adaptive = mu * edge_weight  # 边缘处减小扩散系数
        epsilon_adaptive = epsilon * edge_weight  # 边缘处减小界面宽度
    # 计算图像驱动项
    image_force = lambda_adaptive * (img - 0.5)
    # 如果使用纹理特征，将其融入图像驱动项
    if use_texture_feature and texture_feature is not None:
        image_force += 0.3 * lambda_adaptive * texture_feature
    # 创新点2：边缘保持正则化项
    edge_preservation_term = 0
    if use_edge_preservation and edge_map is not None:
        # 在边缘处增加额外的正则化项，保持边缘的几何特征
        edge_preservation_term = 0.2 * edge_map * grad_phi_norm
    # 更新相场函数
    dphi = mu_adaptive * (epsilon_adaptive * laplacian - (1/epsilon_adaptive) * phi * (1-phi) * (1-2*phi)) + image_force + edge_preservation_term
    phi = phi + dt * dphi
    # 限制phi在[0,1]范围内
    phi = np.clip(phi, 0, 1)

# 算法结束，计算性能统计
end_time = time.time()
final_memory = process.memory_info().rss / 1024 / 1024  # MB
total_time = end_time - start_time
memory_increase = final_memory - initial_memory

# 输出性能统计结果
print("\n" + "="*60)
print("🎯 APSIS Algorithm Performance Statistics")
print("="*60)
print(f"⏱️  Total Runtime: {total_time:.2f} seconds")
print(f"🧠 Initial Memory: {initial_memory:.1f} MB")
print(f"🧠 Final Memory: {final_memory:.1f} MB")
print(f"🧠 Peak Memory: {peak_memory:.1f} MB")
print(f"📈 Memory Increase: {memory_increase:.1f} MB")
print(f"📊 Iterations Completed: {n_iters}")
print(f"⚡ Average Time per Iteration: {total_time/n_iters:.4f} seconds")
print("="*60)

# 二值化得到最终分割结果
apsis_segmented = (phi > 0.05).astype(np.uint8) * 255

plt.figure(figsize=(12, 8), dpi=500)
# 第一个子图：原始图像
plt.subplot(1, 2, 1)
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.title("Original Image", fontsize=14, fontweight='bold')
plt.axis("off")
# 第二个子图：APSIS分割结果
plt.subplot(1, 2, 2)
plt.imshow(cv2.cvtColor(apsis_segmented, cv2.COLOR_BGR2RGB))
plt.title("APSIS", fontsize=14, fontweight='bold')
plt.axis("off")
# 调整布局并显示
plt.tight_layout()
plt.show()

# =====================================================================================
# 特征域不变性理论基础 (Feature Domain Invariance Theory)
# =====================================================================================
# 【核心思想】：
# 通过对比图像数据在图像域和特征域的统计学差异，发现或数据的有效信息主要蕴含在边
# 缘、结构和纹理等底层视觉特征中，这些特征反映了物体的本质属性。相比之下，光照、色彩等
# 表观信息容易受环境干扰，而底层特征在光学系统存在像差或噪声时仍保持稳定。这种抗干扰的
# 稳健性被称为图像特征域不变性，为计算成像的首选优化提供了理论依据。
# 【数学表达】：
# 设图像 I(x,y) 在不同干扰条件下变为 I'(x,y)，但其特征域表示保持不变：
# F_edge[I] ≈ F_edge[I']     (边缘特征不变性)
# F_texture[I] ≈ F_texture[I'] (纹理特征不变性)
# F_structure[I] ≈ F_structure[I'] (结构特征不变性)
# 【实际意义】：
# 一个图片在不同干扰下特征是不变的，但是像素会变。
# 例如：光照变化、噪声干扰、对比度调整等会改变像素值，但边缘位置、纹理模式、
# 结构关系等特征保持稳定，这为基于特征的图像分割提供了理论基础
#方案 1（像素域）：原图 → 灰度图 → 灰度直方图，阈值往往因光照/噪声偏移而失效。
#方案 2（特征域）：原图 → 边缘特征 → 纹理特征 → 结构稳定性 → 特征直方图，统计曲线几乎不受干扰影响，分割更准确。
# =====================================================================================
# 特征域不变性理论可视化演示程序
# =====================================================================================
print("🔬 开始特征域不变性理论演示...")
print("🎯 准备4种基础图像...")

# 1. 基础图像
base_image = otsu_thresh.copy()
# ==================== 干扰函数定义 ====================
def apply_disturbances(input_image):
    """为输入图像应用5种干扰条件"""
    h, w = input_image.shape
    # 云彩光照不均匀1
    np.random.seed(42)
    noise1 = np.random.random((h//4 + 1, w//4 + 1))
    noise2 = np.random.random((h//8 + 1, w//8 + 1))
    cloud_noise1 = cv2.resize(noise1, (w, h))
    cloud_noise2 = cv2.resize(noise2, (w, h))
    cloud_mask1 = 0.6 * cloud_noise1 + 0.4 * cloud_noise2
    cloud_mask1 = gaussian_filter(cloud_mask1, sigma=3)
    cloud_mask1 = 0.4 + 0.8 * (cloud_mask1 - cloud_mask1.min()) / (cloud_mask1.max() - cloud_mask1.min())
    cloud_uneven1 = np.clip(input_image * cloud_mask1, 0, 255).astype(np.uint8)

    # 云彩光照不均匀2
    np.random.seed(123)
    noise3 = np.random.random((h//6 + 1, w//6 + 1))
    cloud_noise3 = cv2.resize(noise3, (w, h))
    cloud_mask2 = gaussian_filter(cloud_noise3, sigma=4)
    cloud_mask2 = 0.3 + 0.9 * (cloud_mask2 - cloud_mask2.min()) / (cloud_mask2.max() - cloud_mask2.min())
    cloud_uneven2 = np.clip(input_image * cloud_mask2, 0, 255).astype(np.uint8)

    # 高斯噪声
    gaussian_noise = np.clip(input_image + np.random.normal(0, 15, input_image.shape), 0, 255).astype(np.uint8)

    # 椒盐噪声
    salt_pepper = input_image.copy()
    noise_ratio = 0.03
    num_salt = int(noise_ratio * input_image.size * 0.5)
    coords = [np.random.randint(0, i-1, num_salt) for i in input_image.shape]
    salt_pepper[coords[0], coords[1]] = 255
    coords = [np.random.randint(0, i-1, num_salt) for i in input_image.shape]
    salt_pepper[coords[0], coords[1]] = 0

    return [input_image, cloud_uneven1, cloud_uneven2, gaussian_noise, salt_pepper]

# ==================== 特征提取函数定义 ====================
def extract_pixel_features(image):
    """提取像素域特征（灰度直方图）"""
    hist, _ = np.histogram(image.flatten(), bins=50, range=(0, 256))
    return hist / np.sum(hist)  # 归一化

def extract_domain_features(image):
    """
    基于您的特征定义的正确量化方式
    边缘特征、纹理特征、特征稳定性 - 基于物理意义量化
    """
    # 归一化到[0,1]
    img = image.astype(np.float64) / 255.0

    # ==================== 1. 边缘特征 ====================
    # 基于您的edge_magnitude计算
    gx = cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)
    gy = cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)
    edge_magnitude = np.sqrt(gx**2 + gy**2)
    edge_magnitude = edge_magnitude / (np.max(edge_magnitude) + 1e-8)

    # 对边缘特征图进行像素统计
    edge_feature_vector = edge_magnitude.flatten()

    # ==================== 2. 纹理特征 ====================
    # 基于您的多尺度高斯差分texture_feature计算
    texture_feature = np.zeros_like(img)
    for sigma in [0.5, 1.0, 2.0]:
        texture_feature += np.abs(img - gaussian_filter(img, sigma))
    texture_feature = texture_feature / (np.max(texture_feature) + 1e-8)

    # 对纹理特征图进行像素统计
    texture_feature_vector = texture_feature.flatten()

    # ==================== 3. 结构特征（特征稳定性） ====================
    # 基于您的特征稳定性feature_stability计算
    local_variance = gaussian_filter(img**2, sigma=1.0) - gaussian_filter(img, sigma=1.0)**2
    feature_stability = 1.0 / (1.0 + 5.0 * local_variance)

    # 对特征稳定性图进行像素统计
    structure_feature_vector = feature_stability.flatten()
    return {
        'edge': edge_feature_vector,
        'texture': texture_feature_vector,
        'structure': structure_feature_vector
    }

# ==================== 像素域和特征域分析 ====================
print("📊 分析基础图像在不同干扰条件下的特征...")

# 应用干扰并计算特征
print("🎯 应用5种干扰条件...")
disturbed_images = apply_disturbances(base_image)
disturbance_names = ['Original Image', 'Cloud Illumination 1', 'Cloud Illumination 2', 'Gaussian Noise', 'Salt-Pepper Noise']
# 计算像素域和特征域特征
all_pixel_features = []
all_domain_features = []
for img in disturbed_images:
    all_pixel_features.append(extract_pixel_features(img))
    all_domain_features.append(extract_domain_features(img))

# ==================== 优化的特征域不变性理论可视化 ====================
print("🎨 生成优化的特征域不变性理论可视化...")

base_names = ['像素域特征']  # 简化为单一基础图像分析
all_distorted_images = [disturbed_images]  # 将干扰图像组织为列表
all_pixel_features_sets = [all_pixel_features]  # 将像素特征组织为列表

# ==================== Create integrated visualization chart for pixel and feature domains ====================
from scipy.stats import gaussian_kde
# Set Times New Roman font for plots
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 10

print("🎨 Generating integrated pixel and feature domain analysis visualization...")
# Create one comprehensive figure with all analyses
fig = plt.figure(figsize=(12,14), dpi=500)
plt.suptitle('Comprehensive Analysis: Pixel Domain and Feature Domain under Different Disturbance Conditions',
             fontsize=18, fontweight='bold', y=0.98)
# Feature domain types and names
feature_types = ['edge', 'texture', 'structure']
feature_names_en = ['Edge Features', 'Texture Features', 'Structure Features']
# Total rows: 1 (images) + 1 (pixel domain) + 3 (feature domains) = 5 rows
total_rows = 5
cols = 5
# Row 1: Display original distorted images
for dist_idx, (img, dist_name) in enumerate(zip(disturbed_images, disturbance_names)):
    plt.subplot(total_rows, cols, dist_idx + 1)
    plt.imshow(img, cmap='gray')
    plt.title(f'{dist_name}', fontsize=12, fontweight='bold')
    plt.axis('off')
# Add row label for images
fig.text(0.02, 0.85, 'Original Images', fontsize=14, fontweight='bold', rotation=90, va='center')
# Row 2: Pixel domain density distributions
for dist_idx, (img, dist_name) in enumerate(zip(disturbed_images, disturbance_names)):
    plt.subplot(total_rows, cols, cols + dist_idx + 1)
    # Calculate Gaussian kernel density estimation for pixel domain
    pixel_values = img.flatten()
    kde = gaussian_kde(pixel_values)
    x_range = np.linspace(0, 255, 100)
    density = kde(x_range)
    # Draw density curve
    color = plt.cm.Set1(dist_idx)
    plt.plot(x_range, density, linewidth=2, color=color, alpha=0.8)
    plt.ylim(0, 0.03)
    plt.yticks(np.arange(0, 0.035, 0.01))
    plt.fill_between(x_range, density, alpha=0.3, color=color)
    plt.title(f'{dist_name}', fontsize=11, fontweight='bold')
    plt.xlabel('Gray Value', fontsize=10)
    plt.ylabel('Density', fontsize=10)
    plt.grid(True, alpha=0.3)
    plt.tick_params(labelsize=9)
# Add row label for pixel domain
fig.text(0.02, 0.68, 'Pixel Domain', fontsize=14, fontweight='bold', rotation=90, va='center')
# Rows 3-5: Feature domain density distributions
for feature_idx, (feature_type, feature_name_en) in enumerate(zip(feature_types, feature_names_en)):
    row_start = (feature_idx + 2) * cols + 1  # Start from row 3
    for dist_idx, (img, dist_name) in enumerate(zip(disturbed_images, disturbance_names)):
        plt.subplot(total_rows, cols, row_start + dist_idx)
        # Get feature domain features of current distorted image
        domain_features = all_domain_features[dist_idx]
        feature_values = domain_features[feature_type]
        # Calculate Gaussian kernel density estimation
        if len(feature_values) > 1 and np.std(feature_values) > 1e-8:  # Ensure sufficient variation
            kde = gaussian_kde(feature_values)
            x_range = np.linspace(np.min(feature_values), np.max(feature_values), 100)
            density = kde(x_range)
            # Draw density curve
            color = plt.cm.Set1(dist_idx)
            plt.plot(x_range, density, linewidth=2, color=color, alpha=0.8)
            plt.fill_between(x_range, density, alpha=0.3, color=color)
            plt.title(f'{dist_name}', fontsize=11, fontweight='bold')
            plt.xlabel(f'{feature_name_en} Value', fontsize=10)
            plt.ylabel('Density', fontsize=10)
            plt.grid(True, alpha=0.3)
            plt.tick_params(labelsize=9)
    # Add row label for each feature domain
    y_positions = [0.51, 0.34, 0.17]  # Approximate y positions for the three feature rows
    fig.text(0.02, y_positions[feature_idx], feature_name_en, fontsize=14, fontweight='bold', rotation=90, va='center')

plt.tight_layout()
plt.subplots_adjust(left=0.08, right=0.98, top=0.95, bottom=0.05, hspace=0.3, wspace=0.3)
plt.show()
print("✅ Integrated pixel and feature domain analysis chart has been generated")

# 方法 6: 自适应泛函相场分割算法 (Adaptive Functional Phase Field Segmentation)
# =====================================================================================
# 【核心理论】空间自适应泛函：
# E[φ] = ∫ w(x)[ε²/2|∇φ|² + (1/ε)W(φ)] + λ(x)F(f(x),φ) dx
# 【关键创新】动态权重自适应泛函：
# - w(x) = μ(x)：扩散权重，边缘处降低扩散，平坦区域保持高扩散去噪
# - λ(x)：图像驱动力权重，边缘处增大驱动力，使界面"紧贴"真实边缘
# - ε(x) = ε₀ × ε_factor(x)：自适应界面宽度，边缘处减小增强锐度
# 【理论优势】：
# - 特征稳定区域：放松平滑约束，利用高扩散去噪和区域凝聚
# - 特征突变区域：加强保边缘性能，精确捕获边缘和纹理细节
# - 自动阈值：使用Otsu方法替代固定阈值，自适应确定最佳分割点
# =====================================================================================
from sklearn.metrics.pairwise import pairwise_kernels
# =============================================================================
# 步骤1: 自适应泛函参数设置和特征提取
# =============================================================================

# 基础参数设置
epsilon_list = [0.05, 0.02]  # 多尺度界面宽度 ε₀
lambda_list = [1.5, 3.0]     # 多尺度图像驱动力基础强度
dt = 0.01                    # 时间步长
n_iters = 500               # 迭代次数
scales = [1.0, 2.0]         # 多尺度分布演化

# 图像预处理
img = homo_image / 255.0    # 归一化到[0,1]
print(f"📷 图像预处理完成，尺寸: {img.shape}")

# 【核心】多维特征提取 - 用于计算空间自适应权重
print("🔍 提取图像特征用于自适应权重计算...")
# 1. 边缘特征：检测图像中的边缘和梯度变化
#    用途：确定扩散权重w(x)和界面宽度调制因子
edge_map = cv2.Sobel(img, cv2.CV_64F, 1, 1, ksize=3)
edge_magnitude = np.sqrt(cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)**2 +
                        cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)**2)
edge_magnitude = edge_magnitude / (np.max(edge_magnitude) + 1e-8)
# 2. 纹理特征：多尺度高斯差分检测纹理变化
#    用途：确定图像驱动力权重λ(x)
texture_feature = np.zeros_like(img)
for sigma in [0.5, 1.0, 2.0]:
    texture_feature += np.abs(img - gaussian_filter(img, sigma))
texture_feature = texture_feature / (np.max(texture_feature) + 1e-8)
# 3. 特征稳定性：局部方差的倒数，衡量区域的平坦程度
#    用途：确定扩散权重w(x)，平坦区域高扩散，变化区域低扩散
local_variance = gaussian_filter(img**2, sigma=1.0) - gaussian_filter(img, sigma=1.0)**2
feature_stability = 1.0 / (1.0 + 5.0 * local_variance)
print(f"   边缘强度范围: [{edge_magnitude.min():.3f}, {edge_magnitude.max():.3f}]")
print(f"   纹理特征范围: [{texture_feature.min():.3f}, {texture_feature.max():.3f}]")
print(f"   特征稳定性范围: [{feature_stability.min():.3f}, {feature_stability.max():.3f}]")

# =============================================================================
# 步骤2: 计算空间自适应权重
# =============================================================================

def compute_adaptive_weights(edge_magnitude, feature_stability, texture_feature):
    """
    根据图像特征计算空间自适应权重

    理论基础：E[φ] = ∫ w(x)[ε²/2|∇φ|² + (1/ε)W(φ)] + λ(x)F(f(x),φ) dx

    核心思想：
    - 特征稳定区域（平坦）：放松平滑约束，利用高扩散去噪和区域凝聚
    - 特征突变区域（边缘）：加强保边缘性能，精确捕获边缘和纹理细节

    Args:
        edge_magnitude: 边缘强度图 [0,1]
        feature_stability: 特征稳定性图 [0,1]
        texture_feature: 纹理特征图 [0,1]

    Returns:
        w_diffusion: 扩散权重 w(x) = μ(x)
        lambda_weight: 图像驱动力权重 λ(x)
        epsilon_factor: 界面宽度调制因子
    """

    # 1. 扩散权重 w(x)：边缘处降低扩散，平坦区域保持高扩散
    #    目标：边缘区域低扩散保边缘，平坦区域高扩散去噪
    w_diffusion = feature_stability * (1.0 - 0.8 * edge_magnitude)
    w_diffusion = np.clip(w_diffusion, 0.1, 1.0)

    # 2. 图像驱动力权重 λ(x)：边缘处增大驱动力，使界面"紧贴"真实边缘
    #    目标：边缘和纹理突变处增强图像驱动力
    lambda_weight = 1.0 + 2.0 * edge_magnitude + 1.5 * texture_feature
    lambda_weight = np.clip(lambda_weight, 0.5, 4.0)

    # 3. 界面宽度调制因子：边缘处减小ε，增强锐度
    #    目标：边缘处窄界面增强锐度，平坦区域宽界面平滑过渡
    epsilon_factor = 1.0 - 0.6 * edge_magnitude
    epsilon_factor = np.clip(epsilon_factor, 0.3, 1.0)

    return w_diffusion, lambda_weight, epsilon_factor

# 计算自适应权重
print("⚖️ 计算空间自适应权重...")
w_diffusion, lambda_weight, epsilon_factor = compute_adaptive_weights(
    edge_magnitude, feature_stability, texture_feature)

print(f"✅ 自适应权重计算完成:")
print(f"   扩散权重 w(x) 范围: [{w_diffusion.min():.3f}, {w_diffusion.max():.3f}]")
print(f"   驱动力权重 λ(x) 范围: [{lambda_weight.min():.3f}, {lambda_weight.max():.3f}]")
print(f"   界面宽度因子 ε_factor(x) 范围: [{epsilon_factor.min():.3f}, {epsilon_factor.max():.3f}]")

# 验证理论预期
flat_regions = feature_stability > 0.8  # 平坦区域
edge_regions = edge_magnitude > 0.5     # 边缘区域

if np.any(flat_regions) and np.any(edge_regions):
    flat_w_mean = w_diffusion[flat_regions].mean()
    edge_w_mean = w_diffusion[edge_regions].mean()
    flat_lambda_mean = lambda_weight[flat_regions].mean()
    edge_lambda_mean = lambda_weight[edge_regions].mean()

    print(f"🔬 理论验证:")
    print(f"   平坦区域: w={flat_w_mean:.3f}, λ={flat_lambda_mean:.3f}")
    print(f"   边缘区域: w={edge_w_mean:.3f}, λ={edge_lambda_mean:.3f}")
    print(f"   边缘扩散权重 < 平坦区域: {edge_w_mean < flat_w_mean} {'✓' if edge_w_mean < flat_w_mean else '✗'}")
    print(f"   边缘驱动力权重 > 平坦区域: {edge_lambda_mean > flat_lambda_mean} {'✓' if edge_lambda_mean > flat_lambda_mean else '✗'}")
feature_stack = np.stack([img, edge_map, texture_feature], axis=-1)  # (H, W, C)

# --- otsu初始化phi ---
_, init_phi = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
phi_list = [init_phi.astype(float)/255.0 for _ in scales]

# --- 定义优化的MMD函数---
def compute_mmd_efficient(X1, X2, kernel='rbf', gamma=1.0, max_samples=1000):
    """
    内存友好的MMD计算，通过采样减少内存消耗
    """
    # 如果样本数量过大，进行随机采样
    if len(X1) > max_samples:
        idx1 = np.random.choice(len(X1), max_samples, replace=False)
        X1 = X1[idx1]
    if len(X2) > max_samples:
        idx2 = np.random.choice(len(X2), max_samples, replace=False)
        X2 = X2[idx2]
    # 使用分块计算避免大矩阵
    def rbf_kernel_chunked(X, Y, gamma, chunk_size=500):
        n_x, n_y = len(X), len(Y)
        result = 0.0
        count = 0
        for i in range(0, n_x, chunk_size):
            for j in range(0, n_y, chunk_size):
                x_chunk = X[i:min(i+chunk_size, n_x)]
                y_chunk = Y[j:min(j+chunk_size, n_y)]
                # 计算欧氏距离的平方
                dist_sq = np.sum((x_chunk[:, None, :] - y_chunk[None, :, :]) ** 2, axis=2)
                # RBF核
                kernel_chunk = np.exp(-gamma * dist_sq)
                result += np.sum(kernel_chunk)
                count += len(x_chunk) * len(y_chunk)

        return result / count if count > 0 else 0.0
    # 计算MMD
    K11 = rbf_kernel_chunked(X1, X1, gamma)
    K22 = rbf_kernel_chunked(X2, X2, gamma)
    K12 = rbf_kernel_chunked(X1, X2, gamma)
    return K11 + K22 - 2 * K12

# --- 多尺度演化 ---
for s, (phi, epsilon, lambda_) in enumerate(zip(phi_list, epsilon_list, lambda_list)):
    for i in range(n_iters):
        grad_phi_x = np.gradient(phi, axis=1)
        grad_phi_y = np.gradient(phi, axis=0)
        grad_phi_norm = np.sqrt(grad_phi_x**2 + grad_phi_y**2)
        laplacian = cv2.Laplacian(phi, cv2.CV_64F)
        # 提取前景/背景特征
        mask_fg = phi > 0.5
        mask_bg = phi <= 0.5
        f_fg = feature_stack[mask_fg].reshape(-1, 3)
        f_bg = feature_stack[mask_bg].reshape(-1, 3)
        if len(f_fg) > 10 and len(f_bg) > 10:
            mmd_loss = compute_mmd_efficient(f_fg, f_bg, gamma=10.0, max_samples=500)
            # 【核心】使用空间自适应λ(x)权重
            mmd_grad = lambda_weight * (2*phi - 1) * mmd_loss
        else:
            mmd_grad = 0
        # 【核心】自适应泛函演化方程
        # E[φ] = ∫ w(x)[ε²/2|∇φ|² + (1/ε)W(φ)] + λ(x)F(f(x),φ) dx
        # 1. 凸项（显式）：空间自适应Laplacian平滑 w(x) * ε(x) * ∇²φ
        epsilon_adaptive = epsilon * epsilon_factor  # 边缘处减小界面宽度
        convex_term = w_diffusion * epsilon_adaptive * laplacian
        # 2. 非凸项（半隐式）：空间自适应势能项 + 特征MMD
        # w(x) * (1/ε(x)) * W'(φ) + λ(x) * F_feature
        double_well_term = phi * (1 - phi) * (1 - 2 * phi)
        nonconvex_term = -w_diffusion * (1/epsilon_adaptive) * double_well_term + mmd_grad
        # 3. 边缘保持项：在边缘区域增强保边缘能力
        edge_preservation = 0.1 * edge_magnitude * grad_phi_norm * np.sign(phi - 0.5)
        # 自适应相场演化
        phi += dt * (convex_term + nonconvex_term + edge_preservation)
        phi = np.clip(phi, 0, 1)
        # 每100次迭代显示进度
        if (i + 1) % 100 == 0:
            print(f"   迭代 {i+1}/{n_iters}, 相场范围: [{phi.min():.3f}, {phi.max():.3f}]")

# 使用Otsu自动阈值进行最终二值化
phi_final = phi_list[-1]
phi_normalized = ((phi_final - phi_final.min()) / (phi_final.max() - phi_final.min()) * 255).astype(np.uint8)
otsu_threshold, _ = cv2.threshold(phi_normalized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
otsu_threshold_normalized = otsu_threshold / 255.0
print(f"🎯 自适应泛函Otsu阈值: {otsu_threshold_normalized:.3f} (原始值: {otsu_threshold})")
# 最终融合（使用Otsu自动阈值）
apsis_segmented2 = (phi_final > otsu_threshold_normalized).astype(np.uint8) * 255
# 定义最终的自适应泛函相场分割结果
apsis_segmented = apsis_segmented2  # 使用自适应泛函方法的结果作为最终结果

# ===================== 最终可视化对比 =====================
plt.figure(figsize=(24, 16), dpi=200)

# 第一行：传统方法对比
# 原始图像
plt.subplot(2, 4, 1)
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.title("原始图像\nOriginal Image", fontsize=14, fontweight='bold')
plt.axis("off")

# 读取标准答案（如果存在）
file_path = 'D:/代码/ICE/label/sea_ice_419.xlsx'
data = pd.read_excel(file_path, header=None, usecols=range(256), nrows=256)
numpy_array = data.to_numpy()
plt.subplot(2, 4, 2)
plt.imshow(numpy_array, cmap="gray")
plt.title("Ground Truth", fontsize=14, fontweight='bold')
plt.axis("off")

# Otsu 阈值分割
plt.subplot(2, 4, 3)
plt.imshow(otsu_thresh, cmap="gray")
plt.title("nOtsu Thresholding", fontsize=14, fontweight='bold')
plt.axis("off")

# Gabor 滤波器 + K-Means 分割
plt.subplot(2, 4, 4)
plt.imshow(segmented_image_kmeans, cmap="gray")
plt.title("Gabor + K-Means", fontsize=14, fontweight='bold')
plt.axis("off")

# Gabor 滤波器 + K-Means 分割
plt.subplot(2, 4, 5)
plt.imshow(labels_rw, cmap="gray")
plt.title("Gabor + K-Means", fontsize=14, fontweight='bold')
plt.axis("off")

# 第二行：相场方法对比
# 简化相场分割
plt.subplot(2, 4, 6)
plt.imshow(apsis_segmented1, cmap="gray_r")
plt.title("Simplified Phase Field\n(Fixed Parameters)", fontsize=11, fontweight='bold')
plt.axis("off")

# 自适应泛函相场分割
plt.subplot(2, 4, 7)
plt.imshow(apsis_segmented2, cmap="gray_r")
plt.title(f"Adaptive Functional\nPhase Field (Otsu)",
         fontsize=11, fontweight='bold')
plt.axis("off")


#===================== 简化评价指标计算 =====================
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, jaccard_score
import pandas as pd

file_path = 'D:/代码/ICE/label/sea_ice_419.xlsx'
data = pd.read_excel(file_path, header=None, usecols=range(256), nrows=256)
ground_truth = np.where(data.to_numpy() == 255, 1, 0)

# 计算指标的简化函数
def calc_metrics(y_true, y_pred):
    y_true_flat = y_true.flatten()
    y_pred_flat = y_pred.flatten()
    return {
        'accuracy': accuracy_score(y_true_flat, y_pred_flat),
        'precision': precision_score(y_true_flat, y_pred_flat, zero_division=0),
        'recall': recall_score(y_true_flat, y_pred_flat, zero_division=0),
        'f1_score': f1_score(y_true_flat, y_pred_flat, zero_division=0),
        'iou': jaccard_score(y_true_flat, y_pred_flat, zero_division=0)
    }

# 计算各方法指标
otsu_binary = np.where(otsu_thresh == 255, 1, 0)
kmeans_binary = np.where(segmented_image_kmeans == 255, 1, 0)
# 使用简化相场作为传统方法的代表
traditional_binary = np.where(apsis_segmented1 > 127, 1, 0)
apsis_binary0 = np.where(apsis_segmented1 > 127, 1, 0)
apsis_binary1 = np.where(apsis_segmented2 > 127, 1, 0)

otsu_metrics = calc_metrics(ground_truth, otsu_binary)
kmeans_metrics = calc_metrics(ground_truth, kmeans_binary)
traditional_metrics = calc_metrics(ground_truth, traditional_binary)
apsis_metrics0 = calc_metrics(ground_truth, apsis_binary0)
apsis_metrics1 = calc_metrics(ground_truth, apsis_binary1)

# =====================高级统计分析功能=====================
# 读取 Excel 文件
file_path =  'D:/代码/ICE/label/sea_ice_419.xlsx'
data = pd.read_excel(file_path, header=None, usecols=range(256), nrows=256)
# 将数据转换为 NumPy 数组
numpy_array = data.to_numpy()
plt.imshow(numpy_array, cmap='gray')
plt.title("Original Grayscale Image")
plt.axis("off")

#0.选择图片分割后的结果
ice_image=apsis_segmented
#0.1图像二值化反转（将黑白颜色反转）
numpy_array = np.where(numpy_array == 0, 255, np.where(numpy_array == 255, 0, numpy_array))
ice_image=numpy_array
# 1. 海冰连通区域识别与可视化
#使用分割得到的标签图，每个连通区域可以视为一块独立的海冰。
# 识别连通区域,进行单一的颜色识别
labeled_ice, num_features = ndimage.label(ice_image)
print(f"共识别出 {num_features} 块独立海冰")
# 将labeled_ice中大于0的值替换为1
binary_ice = np.where(labeled_ice > 0, 1, 0)
plt.figure(figsize=(20, 15),dpi=200)

# 图片的冰区覆盖率
ice_area = np.sum(ice_image > 0)  # 冰层区域的面积
total_area = ice_image.size  # 图像的总面积
coverage = (ice_area / total_area) * 100  # 覆盖率（百分比）
print(f"海冰覆盖率: {coverage:.2f}%")

# 可视化每块海冰
fig = plt.figure(figsize=(10, 8), dpi=500)
# 原始图像
ax1 = plt.subplot(2, 3, 1)
ax1.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
ax1.set_title("Original Image", fontsize=12)
ax1.axis("off")
# 二值化海冰图像
ax2 = plt.subplot(2, 3, 2)
ax2.imshow(binary_ice, cmap='gray_r')
ax2.set_title(f"Sea Ice: {num_features} blocks\nCoverage Rate: {coverage:.2f}%", fontsize=12)
ax2.axis("off")
plt.tight_layout()

# 识别每个连通区域的边界框
objects = ndimage.find_objects(labeled_ice)
# 绘制红色方框标记每块海冰
for i, obj_slice in enumerate(objects, 1):
    if obj_slice is not None:
        y_start, y_stop = obj_slice[0].start, obj_slice[0].stop
        x_start, x_stop = obj_slice[1].start, obj_slice[1].stop
        width = x_stop - x_start
        height = y_stop - y_start
        # 添加红色方框
        rect = patches.Rectangle((x_start, y_start), width, height, 
                                linewidth=2, edgecolor='red', facecolor='none')
        ax2.add_patch(rect)
        # # 添加编号标签
        # ax.text(x_start, y_start-5, f"{i}", color='white', fontweight='bold', 
        #         backgroundcolor='red', fontsize=8)
plt.show()

#2.1 海冰厚度的估计（基于灰度特征）
#厚度估算改进： 如果有真实厚度数据，可以通过回归模型建立更精准的灰度-厚度映射。
#假设灰度值与厚度呈一定的物理关系，可以通过以下方式估算：
print("开始估算海冰厚度...")
# 假设 gray_image 是你的冰厚图像
scaler = MinMaxScaler(feature_range=(0, 1))  # 可以调整范围，例如(0,5)表示0-5米厚度
# 将 gray_image 扁平化为一维数组，MinMaxScaler 要求二维数据
thickness=gray_image
gray_image_flattened = thickness.flatten().reshape(-1, 1)
# 对数据进行归一化
normalized_gray_image_flattened = scaler.fit_transform(gray_image_flattened)
# 将归一化后的图像重新调整为原来的形状
normalized_image = normalized_gray_image_flattened.reshape(ice_image.shape)
thickness_image = normalized_image  # 冰厚结果
# 可视化厚度分布
plt.figure(figsize=(5, 5))
plt.imshow(thickness_image, cmap='jet', origin='upper')
plt.title("海冰厚度估计分布", fontsize=14)
plt.colorbar(label='估计厚度 (归一化单位)')
plt.axis('off')
plt.show()

# 计算每块海冰的平均灰度值（作为厚度的近似）
h, w = gray_image.shape
x = np.linspace(0, w, w)
y = np.linspace(0, h, h)
x, y = np.meshgrid(x, y)  # 生成网格坐标
#z = gray_image  # 将灰度值作为 z 高度
z = enhanced_image  # 将灰度值作为 z 高度
# 创建 3D 绘图
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')
# 绘制曲面
surface = ax.plot_surface(x, y, z, cmap="gray", edgecolor='none')
# 添加颜色条
fig.colorbar(surface, ax=ax, shrink=0.5, aspect=10)
# 设置标题和轴标签
ax.set_title("3D Visualization of Grayscale Image")
ax.set_xlabel("X-axis")
ax.set_ylabel("Y-axis")
ax.set_zlabel("Pixel Intensity")
plt.show()
thickness_list = ndimage.mean(thickness_image, labels=labeled_ice, index=range(1, num_features + 1))

ice_image=poly_mask

# 2.3 多尺度冰层覆盖率密集度分析
print("计算多尺度海冰密集度分布...")
def calculate_multiscale_ice_density(ice_image, scales=[20, 50, 100]):
    from scipy.ndimage import gaussian_filter
    h, w = ice_image.shape
    density_maps = []
    # 计算不同尺度的密度图
    for window_size in scales:
        # 调整高斯平滑参数与窗口大小成比例
        sigma = max(1, window_size / 20)
        # 使用卷积计算局部密度
        from scipy.signal import convolve2d
        kernel = np.ones((window_size, window_size))
        local_sum = convolve2d(ice_image > 0, kernel, mode='same')
        max_sum = window_size * window_size
        # 计算百分比并应用高斯平滑
        density_map = (local_sum / max_sum) * 100
        density_map = gaussian_filter(density_map, sigma=sigma)
        density_maps.append(density_map)
    return density_maps

# 计算多尺度密度
scales = [20, 40, 70, 100]  # 从小尺度到大尺度
density_maps= calculate_multiscale_ice_density(ice_image, scales)
density_maps123= calculate_multiscale_ice_density(ice_image)

# im2 = ax2.imshow(density_maps123, cmap='coolwarm', alpha=1)
# ax2.set_title('Ice distribution density(scale=20)', fontsize=14,fontname='Times New Roman')
# ax2.axis('off')

# 将密度图缩放到0-1区间
def normalize_to_01(matrix):
    """将矩阵值缩放到0-1区间"""
    min_val = np.min(matrix)
    max_val = np.max(matrix)
    if max_val > min_val:
        return (matrix - min_val) / (max_val - min_val)
    return matrix  # 如果矩阵是常数，则返回原矩阵

# 对密度图进行归一化
density_map_sliding1 = normalize_to_01(density_maps[0])
density_map_sliding2 = normalize_to_01(density_maps[1])
density_map_sliding3 = normalize_to_01(density_maps[2])
density_map_sliding4 = normalize_to_01(density_maps[3])
density_map_slidings=[density_map_sliding1,density_map_sliding2,density_map_sliding3,density_map_sliding4]
# 融合多尺度密度图 - 使用加权平均
weights = np.array([1/len(scales)] * len(scales))  # 均等权重
fused_density = np.zeros_like(ice_image, dtype=float)
for i, density_map in enumerate(density_maps):
    fused_density += density_map * weights[i]
density_map_sliding = normalize_to_01(fused_density)

from matplotlib import font_manager
# 可视化每块海冰厚度和密集度
fig = plt.figure(figsize=(9, 8), dpi=500)
# 原始图像
# ax1 = plt.subplot(2, 3, 1)
# im1 = ax1.imshow(thickness_image, cmap='coolwarm')
# ax1.set_title("Thickness", fontsize=14)
# ax1.axis("off")
ax1 = plt.subplot(2, 3, 1)
im1 = ax1.imshow(thickness_image, cmap='coolwarm')
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
ax1.set_title('Original image', fontsize=14,fontname='Times New Roman')
plt.axis('off')
# 密度图
ax2 = plt.subplot(2, 3, 2)
im2 = ax2.imshow(density_map_sliding1, cmap='coolwarm', alpha=1)
ax2.set_title('Ice distribution density(scale=20)', fontsize=14,fontname='Times New Roman')
ax2.axis('off')
# 密度图
ax3 = plt.subplot(2, 3, 3)
im3 = ax3.imshow(density_map_sliding2, cmap='coolwarm', alpha=1)
ax3.set_title('Ice distribution density(scale=50)', fontsize=14,fontname='Times New Roman')
ax3.axis('off')
# 密度图
ax4 = plt.subplot(2, 3, 4)
im4 = ax4.imshow(density_map_sliding3, cmap='coolwarm', alpha=1)
ax4.set_title('Ice distribution density(scale=70)', fontsize=14,fontname='Times New Roman')
ax4.axis('off')   
# 密度图
ax5 = plt.subplot(2, 3, 5)
im5 = ax5.imshow(density_map_sliding4, cmap='coolwarm', alpha=1)
ax5.set_title('Ice distribution density(scale=100)', fontsize=14,fontname='Times New Roman')
ax5.axis('off')
# 密度图
ax6 = plt.subplot(2, 3, 6)
im6 = ax6.imshow(density_map_sliding, cmap='coolwarm', alpha=1)
ax6.set_title('Ice distribution density', fontsize=14,fontname='Times New Roman')
ax6.axis('off')
# 调整布局
plt.tight_layout(rect=[0, 0.01, 1, 0.95])  # 为颜色条留出空间
# 添加统一的颜色条在底部
cbar_ax = fig.add_axes([0.15, 0.02, 0.7, 0.02])  # 调整位置和大小
colorbar = fig.colorbar(im6, cax=cbar_ax, orientation='horizontal')  # 使用最后一个图像的颜色映射
# 设置颜色条刻度的字体
font_prop = font_manager.FontProperties(family='Times New Roman', size=12)
colorbar.ax.tick_params(labelsize=12)  # 调整刻度字体大小
for label in colorbar.ax.get_xticklabels():
    label.set_fontproperties(font_prop)  # 应用 Times New Roman
# 添加颜色条标题
#colorbar.set_label('Density', fontproperties=font_prop, size=14)
# 显示图表
plt.show()

# ===================== 狭窄区域量化功能 =====================
def analyze_narrow_passages(ice_image, min_width_threshold=5, max_width_threshold=20):
    """
    分析并量化海冰之间的狭窄通道
    
    参数:
        ice_image: 二值化的海冰图像 (1表示海冰，0表示水域)
        min_width_threshold: 最小宽度阈值，小于此值被视为极度狭窄
        max_width_threshold: 最大宽度阈值，大于此值被视为宽阔通道
    
    返回:
        narrowness_map: 狭窄度量图 (值越大表示越狭窄)
        passage_width_map: 通道宽度图
        skeleton: 通道骨架
    """
    from scipy.ndimage import distance_transform_edt
    from skimage.morphology import skeletonize
    
    # 1. 反转图像，使海冰为0，通道为1
    passage_space = np.where(ice_image > 0, 0, 1)
    
    # 2. 计算距离变换 - 每个点到最近障碍物(海冰)的距离
    dist_transform = distance_transform_edt(passage_space)
    
    # 3. 提取通道骨架 - 通道的中心线
    skeleton = skeletonize(passage_space)
    
    # 4. 计算骨架上各点的通道宽度 (直径)
    passage_width_map = np.zeros_like(dist_transform)
    passage_width_map[skeleton > 0] = 2 * dist_transform[skeleton > 0]  # 直径 = 2 * 半径
    
    # 5. 创建狭窄度量图 (越狭窄值越大)
    # 使用反比例关系计算狭窄度：narrowness = 1/width
    narrowness_map = np.zeros_like(dist_transform, dtype=float)
    
    # 仅在骨架上计算狭窄度
    valid_points = (skeleton > 0)
    widths = passage_width_map[valid_points]
    
    # 使用反比例关系计算狭窄度，并归一化到[0,1]区间
    max_width = np.max(widths) if np.any(widths) else 1
    narrowness = 1.0 - (widths / max(max_width, 1.0))
    
    narrowness_map[valid_points] = narrowness
    
    # 6. 对狭窄度进行分类 (仅用于统计，不影响狭窄度计算)
    extreme_narrow = (passage_width_map > 0) & (passage_width_map < min_width_threshold)
    moderate_narrow = (passage_width_map >= min_width_threshold) & (passage_width_map < max_width_threshold)
    wide_passage = (passage_width_map >= max_width_threshold)
    
    # 7. 统计各类通道的数量和比例
    total_passage_pixels = np.sum(skeleton)
    if total_passage_pixels > 0:
        extreme_narrow_count = np.sum(extreme_narrow & (skeleton > 0))
        moderate_narrow_count = np.sum(moderate_narrow & (skeleton > 0))
        wide_passage_count = np.sum(wide_passage & (skeleton > 0))
        
        print(f"通道统计:")
        print(f"  - 极度狭窄区域 (<{min_width_threshold}像素): {extreme_narrow_count} 像素 ({extreme_narrow_count/total_passage_pixels*100:.1f}%)")
        print(f"  - 中度狭窄区域 ({min_width_threshold}-{max_width_threshold}像素): {moderate_narrow_count} 像素 ({moderate_narrow_count/total_passage_pixels*100:.1f}%)")
        print(f"  - 宽阔通道 (>{max_width_threshold}像素): {wide_passage_count} 像素 ({wide_passage_count/total_passage_pixels*100:.1f}%)")
    else:
        print("未检测到通道")
    
    return narrowness_map, passage_width_map, skeleton, extreme_narrow, moderate_narrow, wide_passage

# 在路径规划前分析狭窄区域
print("\n分析海冰之间的狭窄通道...")
narrowness_map, passage_width_map, skeleton, extreme_narrow, moderate_narrow, wide_passage = analyze_narrow_passages(
    ice_image, min_width_threshold=5, max_width_threshold=20)

from matplotlib import font_manager
# 设置字体属性
font_properties = font_manager.FontProperties(family='Times New Roman')
# 可视化狭窄区域分析结果
plt.figure(figsize=(7, 6),dpi=800)
# 原始海冰图像
# plt.subplot(2, 2, 1)
# plt.imshow(ice_image, cmap='gray')
# plt.title("原始海冰图像",fontname='Times New Roman')
# plt.axis('off')
# 通道骨架
plt.subplot(2, 2, 1)
plt.imshow(skeleton, cmap='gray')
plt.title("Channel skeleton",fontname='Times New Roman')
plt.colorbar()
plt.axis('off')
# 通道宽度图
plt.subplot(2, 2, 2)
#plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
plt.imshow(passage_width_map, cmap='coolwarm')
cbar = plt.colorbar(label='Channel width')
cbar.ax.tick_params(labelsize=10)
cbar.ax.set_ylabel('Channel width', fontproperties=font_properties)
cbar.ax.yaxis.set_tick_params(labelsize=10)
for label in cbar.ax.get_yticklabels():
    label.set_fontproperties(font_properties)
plt.title("Channel width distribution",fontname='Times New Roman')
plt.axis('off')
# 狭窄度量图
plt.subplot(2, 2, 3)
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
# 使用alpha通道使狭窄区域更明显
alpha_map = np.zeros_like(narrowness_map)
alpha_map[skeleton > 0] = 0.7  # 设置透明度
narrowness_map=normalize_to_01(narrowness_map)
plt.imshow(narrowness_map, cmap='coolwarm', alpha=alpha_map)
cbar = plt.colorbar(label='Narrowness degree')
cbar.ax.tick_params(labelsize=10)
cbar.ax.set_ylabel('Narrowness degree', fontproperties=font_properties)
cbar.ax.yaxis.set_tick_params(labelsize=10)
for label in cbar.ax.get_yticklabels():
    label.set_fontproperties(font_properties)
plt.title("Narrowness map",fontname='Times New Roman')
plt.axis('off')
# 狭窄区域分类
# classified_map = np.zeros_like(passage_width_map)
# classified_map[extreme_narrow] = 3  # 极度狭窄
# classified_map[moderate_narrow] = 2  # 中度狭窄
# classified_map[wide_passage] = 1  # 宽阔通道
# plt.subplot(2, 3, 5)
# cmap = plt.cm.get_cmap('viridis', 4)
# im = plt.imshow(classified_map, cmap=cmap, vmin=0, vmax=3)
# cbar = plt.colorbar(im, ticks=[0, 1, 2, 3])
# cbar.set_ticklabels(['背景', '宽阔通道', '中度狭窄', '极度狭窄'])
# plt.title("通道分类")
# plt.axis('off')
# 叠加在原图上显示
plt.subplot(2, 2, 4)
# 创建狭窄区域风险图，用于路径规划
narrow_risk_map = np.zeros_like(ice_image, dtype=float)
# 将狭窄度量扩散到周围区域，使路径规划时能够提前避开狭窄区域
from scipy.ndimage import gaussian_filter
narrow_risk_map = gaussian_filter(narrowness_map, sigma=3) * 5  # 扩散并放大风险值
narrow_risk_map=normalize_to_01(narrow_risk_map)
#plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
# 使用alpha通道使狭窄区域更明显
alpha_map = np.zeros_like(narrowness_map)
alpha_map[skeleton > 0] = 0.7  # 设置透明度
plt.imshow(narrow_risk_map, cmap='coolwarm')
cbar = plt.colorbar(label='Narrow risk value')
cbar.ax.tick_params(labelsize=10)
cbar.ax.set_ylabel('Narrow risk value', fontproperties=font_properties)
cbar.ax.yaxis.set_tick_params(labelsize=10)
for label in cbar.ax.get_yticklabels():
    label.set_fontproperties(font_properties)
plt.title("Narrow region risk map",fontname='Times New Roman')
plt.axis('off')
plt.tight_layout()
plt.show()

# # ===================== 单一海冰信息功能 =====================
# # 单一海冰信息
# from ice_information import analyze_sea_ice
# iceinformation=analyze_sea_ice(ice_image, gray_image=gray_image, show_plots=True, interactive=True)
# # 整体海冰信息
# from ice_features_matrix import create_feature_matrices
# feature_matrices = create_feature_matrices(ice_image, gray_image, return_list=False)
# feature_matrices_list = create_feature_matrices(ice_image, gray_image, return_list=True)
# from matplotlib.ticker import MaxNLocator
# from matplotlib.gridspec import GridSpec
# # 创建左右布局的图表，左侧为热图，右侧为统计直方图
# plt.figure(figsize=(8, 4), dpi=500)
# gs = GridSpec(1, 2, width_ratios=[1.2, 1], figure=plt.gcf())#, wspace=0.1)
# # 左侧热图
# ax1 = plt.subplot(gs[0])
# feature_name = 'thickness'  # 可以改为其他特征: area, fractal_dimension, complexity, texture_contrast, thickness,
# im = ax1.imshow(feature_matrices[feature_name], cmap='coolwarm')
# feature_name_image='thickness'
# ax1.set_title(f'Spatial characteristics of {feature_name_image}', fontsize=12)
# ax1.axis("off")
# # 添加颜色条并设置在图下方
# cbar = plt.colorbar(im, ax=ax1, orientation='horizontal', pad=0.05, fraction=0.046, shrink=0.8)
# cbar.ax.tick_params(labelsize=10)  # 调整刻度标签大小
# # 右侧直方图
# ax2 = plt.subplot(gs[1])
# # 使用labeled_ice提取每个海冰块的特征值
# unique_labels = np.unique(labeled_ice)[1:]  # 排除背景（标签0）
# feature_values = []
# for label in unique_labels:
#     # 创建当前海冰块的掩码
#     mask = (labeled_ice == label)
#     # 提取该海冰块在特征矩阵中的平均值
#     feature_value = np.mean(feature_matrices[feature_name][mask])
#     feature_values.append(feature_value)
# # 转换为numpy数组
# feature_values = np.array(feature_values)
# # 过滤掉零值和NaN值
# valid_data = feature_values[feature_values > 0]
# valid_data = valid_data[~np.isnan(valid_data)]
# # 绘制直方图
# sns.histplot(valid_data, bins=20, kde=True, ax=ax2, color='skyblue')
# ax2.set_title(f'Statistical histogram of {feature_name_image}', fontsize=12)
# feature_name_image1='Thickness'
# ax2.set_xlabel(f'{feature_name_image1}', fontsize=12)
# ax2.set_ylabel('Frequency',fontsize=12)
# # 添加中位数和平均值线
# median_value = np.median(valid_data)
# mean_value = np.mean(valid_data)
# ax2.axvline(median_value, color='r', linestyle='--', label=f'median_value: {median_value:.3f}',)
# ax2.axvline(mean_value, color='b', linestyle='-.', label=f'mean_value: {mean_value:.3f}',)
# ax2.legend(fontsize=12)
# plt.tight_layout()
# plt.show()

# ===================== 路径规划功能 =====================
#1.1 海冰的膨胀安全距离
#定义膨胀操作以模拟 "充气" 海冰，保证安全距离
safety_margin = 1  # 安全边距（可根据实际需求调整）
# 膨胀操作
start_time = time.time()
inflated_ice = binary_dilation(ice_image, iterations=safety_margin)
print(f"膨胀操作耗时: {time.time() - start_time:.4f}秒")

#创建加权图像，初步为空白区域（0值）和冰厚区域（根据 gray_image 加权）
weighted_image = np.copy(ice_image)
#设置膨胀区域为障碍物，其他空白区域根据冰厚值加权
weighted_image[inflated_ice == 1] = 1  # 赋予1（表示海冰区域）
weighted_image[inflated_ice == 0] = 0  # 赋予0（表示非海冰区域）

# 可视化膨胀后的海冰区域
plt.figure(figsize=(8, 6))
plt.imshow(weighted_image, cmap='jet', origin='upper')
plt.title("膨胀后的海冰区域（安全距离考虑）", fontsize=14)
plt.colorbar(label='海冰标记')
plt.axis('off')
plt.show()

#1.2 海冰的膨胀后的栅格定义
# 连通区域标记
labeled_inflated, num_inflated = ndimage.label(weighted_image)
#labeled_inflated, num_inflated = ndimage.label(ice_image)
print(f"膨胀后共有 {num_inflated} 块连通区域")

# 方案1：矩形包围框及掩膜处理
rect_mask = np.zeros_like(ice_image)
fig, ax = plt.subplots(figsize=(10, 8))
ax.imshow(ice_image, cmap='gray')
ax.set_title(f"矩形安全边界框: {num_inflated}块", fontsize=14)
ax.axis("off")

objects_inflated = ndimage.find_objects(labeled_inflated)
# 存储所有矩形区域的信息，用于后续分析
rect_info = []
for i, obj_slice in enumerate(objects_inflated, 1):
    if obj_slice:
        y_start, y_stop = obj_slice[0].start, obj_slice[0].stop
        x_start, x_stop = obj_slice[1].start, obj_slice[1].stop
        width = x_stop - x_start
        height = y_stop - y_start
        rect = patches.Rectangle((x_start, y_start), 
                                width, height,
                                linewidth=1, edgecolor='cyan', 
                                facecolor='none', linestyle='--')
        ax.add_patch(rect)
        # 添加编号标签
        ax.text(x_start, y_start-5, f"{i}", color='cyan', fontweight='bold', fontsize=8)
        # 赋值为1（创建矩形掩膜）
        rect_mask[y_start:y_stop, x_start:x_stop] = 1
        # 存储矩形信息
        rect_info.append({
            'id': i,
            'x_start': x_start,
            'y_start': y_start,
            'width': width,
            'height': height,
            'area': width * height
        })
plt.show()

# 方案2：凸包和椭圆包围框处理
poly_mask = np.zeros_like(weighted_image)
#poly_mask = np.zeros_like(ice_image)
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 8),dpi=500)
# 第一个子图：凸包包围
ax1.imshow(ice_image, cmap='gray')
ax1.set_title(f"凸包安全边界: {num_inflated}块", fontsize=14)
ax1.axis("off")
# 第二个子图：椭圆包围
ax2.imshow(ice_image, cmap='gray')
ax2.set_title(f"椭圆安全边界: {num_inflated}块", fontsize=14)
ax2.axis("off")
objects_inflated = ndimage.find_objects(labeled_inflated)
shape_info = []  # 存储所有区域的信息

for i, obj_slice in enumerate(objects_inflated, 1):
    if obj_slice:
        # 获取当前对象的掩码
        obj_mask = (labeled_inflated[obj_slice] == i)
        y_indices, x_indices = np.nonzero(obj_mask)
        
        if len(x_indices) < 3:  # 跳过太小的对象
            continue
            
        # 获取轮廓点
        y_offset = obj_slice[0].start
        x_offset = obj_slice[1].start
        points = np.column_stack((x_indices + x_offset, y_indices + y_offset))
        
        try:
            # 1. 计算凸包
            hull = ConvexHull(points)
            hull_points = points[hull.vertices]
            
            # 绘制凸包
            hull_patch = patches.Polygon(hull_points, 
                                       linewidth=1, 
                                       edgecolor='cyan',
                                       facecolor='none', 
                                       linestyle='--')
            ax1.add_patch(hull_patch)
            ax1.text(np.min(points[:,0]), np.min(points[:,1])-5, 
                    f"{i}", color='cyan', fontweight='bold', fontsize=8)
            
            # 2. 计算最佳拟合椭圆
            center = np.mean(points, axis=0)
            covariance = np.cov(points.T)
            eigenvals, eigenvecs = np.linalg.eig(covariance)
            
            # 计算椭圆参数
            angle = np.degrees(np.arctan2(eigenvecs[1,0], eigenvecs[0,0]))
            width, height = 4 * np.sqrt(eigenvals)  # 4倍标准差以包含大部分点
            
            # 绘制椭圆
            ellipse = patches.Ellipse(center, width, height,
                                    angle=angle,
                                    linewidth=1,
                                    edgecolor='magenta',
                                    facecolor='none',
                                    linestyle='--')
            ax2.add_patch(ellipse)
            ax2.text(center[0], center[1]-height/2-5,
                    f"{i}", color='magenta', fontweight='bold', fontsize=8)
            
            # 创建掩膜（使用凸包）
            hull_path = Path(hull_points)
            x_min, y_min = np.min(hull_points, axis=0)
            x_max, y_max = np.max(hull_points, axis=0)
            x_coords, y_coords = np.meshgrid(np.arange(x_min, x_max+1),
                                           np.arange(y_min, y_max+1))
            points = np.column_stack((x_coords.ravel(), y_coords.ravel()))
            mask_indices = hull_path.contains_points(points)
            mask_coords = points[mask_indices].astype(int)
            poly_mask[mask_coords[:,1], mask_coords[:,0]] = 1
            
            # 存储形状信息
            shape_info.append({
                'id': i,
                'center': center,
                'hull_area': hull.area,
                'ellipse_width': width,
                'ellipse_height': height,
                'ellipse_angle': angle,
                'is_convex': True
            })
            
        except Exception as e:
            # 如果凸包计算失败，使用矩形边界
            min_x, min_y = np.min(points, axis=0)
            max_x, max_y = np.max(points, axis=0)
            rect_points = np.array([
                [min_x, min_y],
                [max_x, min_y],
                [max_x, max_y],
                [min_x, max_y]
            ])
            
            # 绘制矩形
            rect = patches.Polygon(rect_points,
                                 linewidth=1,
                                 edgecolor='red',
                                 facecolor='none',
                                 linestyle=':')
            ax1.add_patch(rect)
            ax1.text(min_x, min_y-5, 
                    f"{i}", color='red', fontweight='bold', fontsize=8)
            
            # 存储形状信息
            shape_info.append({
                'id': i,
                'center': np.mean(rect_points, axis=0),
                'width': max_x - min_x,
                'height': max_y - min_y,
                'is_convex': False
            })

plt.tight_layout()
plt.show()

# 添加原始图像、膨胀图像和矩形掩膜的对比图
plt.figure(figsize=(10, 10), dpi=500)
# 原始RGB图像
plt.subplot(1, 4, 1)
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB), origin='upper')
plt.title("Original image", fontsize=14, fontname='Times New Roman')
plt.axis('off')
# 原始海冰图像
plt.subplot(1, 4, 2)
plt.imshow(ice_image, cmap='gray_r', origin='upper')
plt.title("APSIS", fontsize=14, fontname='Times New Roman')
plt.axis('off')
# 膨胀后的海冰图像
plt.subplot(1, 4, 3)
plt.imshow(weighted_image, cmap='gray_r', origin='upper')
plt.title("Inflated safety mask", fontsize=14, fontname='Times New Roman')
plt.axis('off')
# 矩形掩膜图像
# plt.subplot(1, 5, 4)
# plt.imshow(rect_mask, cmap='gray_r', origin='upper')
# plt.title("Rectangle safety mask", fontsize=14, fontname='Times New Roman')
# plt.axis('off')
# 多边形掩膜图像
plt.subplot(1, 4, 4)
plt.imshow(poly_mask, cmap='gray_r', origin='upper')
plt.title("Convex hull safety mask", fontsize=14, fontname='Times New Roman')
plt.axis('off')
plt.tight_layout()
plt.show()
# =============================================================================
# 分类 A: 基于图的搜索算法 (Graph-Based Search Algorithms)
# =============================================================================
# 1. Dijkstra 算法（DijkstraClassic）
# 描述：
#   - 基于广度优先搜索，逐步扩展节点，计算起点到所有节点的最短路径。
# 优点：
#   - 全局最优：在边权非负的图中保证找到最优路径。
#   - 完整性：只要存在一条可达路径，算法必能找到。
# 缺点：
#   - 计算量大：在大规模图或高分辨率网格中，搜索节点数激增，计算时间较长。
#   - 无启发信息：对目标信息没有利用，搜索效率相对较低。
from DijkstraClassic import DijkstraClassic
# 2. A* 算法（AstarClassic）
# 描述：
#   - 在 Dijkstra 的基础上引入启发式函数 h(n)，使搜索过程朝向目标方向引导。
# 优点：
#   - 高效性：合理的启发函数可大幅缩小搜索空间，提高搜索速度。
#   - 最优性和完整性：当启发函数满足一致性条件时，能保证全局最优解。
# 缺点：
#   - 启发函数依赖：算法性能受启发函数设计影响较大，不当设计可能影响效果。
#   - 内存消耗大：需保存开放列表和闭合列表，处理高分辨率问题时内存开销较高。
from AstarClassic import AstarClassic
# 3. 最佳优先搜索（Best-First Search）（Best_FirstClassic）
# 描述：
#   - 利用启发函数评估节点的重要性，优先扩展最有希望到达目标的节点。
# 优点：
#   - 搜索速度快：若启发函数设计合理，能迅速指引搜索方向。
# 缺点：
#   - 最优性不保证：可能因启发函数的局部最优性而错失全局最优路径。
#   - 强依赖启发信息：启发函数设计不当时，性能大打折扣。
#from Best_FirstClassic import Best_FirstClassic
# 4. 宽度优先搜索 (BFS)
# 描述：
#   - 逐层展开搜索，不考虑移动代价，从而保证搜索到的路径步数最少。
# 优点：
#   - 保证最少步数：能找到步数最少的路径（适用于所有移动代价相同的情况）。
#   - 算法简单且完整：实现简单，只要路径存在必定能找到。
# 缺点：
#   - 忽略移动代价：不适用于代价不统一的场景，不能保证整体代价最小。
#from BFS import BFS
# 5. 迭代加深 A*（IDA*）（IDAStarClassic）
# 描述：
#   - 结合迭代加深和 A* 的思想，通过不断提高 f = g + h 的阈值来搜索路径。
# 优点：
#   - 内存效率高：相比标准 A*，IDA* 不需存储庞大的开放列表，适合内存受限场景。
# 缺点：
#   - 重复计算：可能多次重复搜索同一节点，导致计算量增加。
#   - 对阈值敏感：搜索效率和成功率依赖于阈值设定策略。
#from IDA import IDAStarClassic
# 6. Theta* 算法（ThetastarClassic）
# 描述：
#   - 类似于 A* 算法，但通过视线检测实现路径平滑，适合网格障碍物环境。
# 优点：
#   - 平滑路径：能够生成较为直接和连续的路径。
#   - 保留 A* 的优势：在满足一定条件下依然能保证最优性。
# 缺点：
#   - 算法复杂：实现上比标准 A* 更复杂，尤其在处理复杂障碍时。
#   - 计算开销：在障碍密集区域，视线检测可能导致额外计算负担。
from ThetastarClassic import ThetastarClassic
#from LPAstar import LPAstar
# =============================================================================
# 分类 B: 采样（随机）基方法 (Sampling-Based Methods)
# =============================================================================
# 1. 快速探索随机树（RRT）
# 描述：
#   - 从起点开始，通过在连续空间中随机采样并扩展树来快速探索整个搜索空间。
# 优点：
#   - 探索效率高：适合高维空间和复杂约束问题，能快速覆盖大部分自由空间。
#   - 实现简单：算法直观，编码实现较为容易。
# 缺点：
#   - 路径不最优：生成的路径通常较长且转弯多，需要后续平滑处理。
#   - 结果依赖随机性：每次运行结果可能有所不同。
# RRT和RRT*算法已在上面定义
#RRT 和概率路线图方法 （PRM） 是运动规划中常用的两种算法
# =============================================================================
# 分类 C: 仿生智能算法 (Bio-Inspired Algorithms)
# =============================================================================
# 1. 蚁群算法（Ant Colony Optimization，ACO）
# 描述：
#   - 模拟蚂蚁觅食行为，通过信息素的正反馈机制在图上搜索路径。
# 优点：
#   - 鲁棒性强：能在复杂、多变的环境中找到较优路径，具备自适应能力。
#   - 并行处理：算法具备天然并行特性，可通过并行计算加速搜索。
# 缺点：
#   - 计算密集：计算量大，收敛速度较慢。
#   - 易陷局部最优：参数选择不当可能导致收敛到次优解，需要精心调参。
# ===================== 导入python_motion_planning算法 =====================
current_dir = os.getcwd()
from python_motion_planning.global_planner.evolutionary_search.aco import ACO
from python_motion_planning.global_planner.evolutionary_search.pso import PSO
from python_motion_planning.global_planner.sample_search.rrt import RRT
from python_motion_planning.global_planner.sample_search.rrt_star import RRTStar
from python_motion_planning.utils import Env, Grid, Node, Map

# ===================== 适配器函数 =====================
def create_grid_from_map(grid_map):
    """从网格地图创建Grid对象（用于ACO/PSO）"""
    # 创建Grid对象，python_motion_planning使用(x_range, y_range)格式
    x_range = grid_map.shape[1]  # 列数
    y_range = grid_map.shape[0]  # 行数

    # 创建Grid对象
    grid = Grid(x_range, y_range)

    # 收集障碍物坐标，转换坐标格式
    obstacles = set()
    for i in range(y_range):
        for j in range(x_range):
            if grid_map[i, j] == 1:  # 障碍物
                obstacles.add((j, i))  # 转换为(x, y)格式

    # 更新障碍物（包含边界障碍物）
    grid.update(obstacles.union(grid.obstacles))
    return grid

def create_map_from_grid(grid_map):
    """从网格地图创建Map对象（用于RRT/RRT*）"""
    # 创建Map对象
    x_range = grid_map.shape[1]  # 列数
    y_range = grid_map.shape[0]  # 行数

    map_env = Map(x_range, y_range)

    # 将网格障碍物转换为矩形障碍物
    obs_rect = []
    for i in range(y_range):
        for j in range(x_range):
            if grid_map[i, j] == 1:  # 障碍物
                # 每个障碍物网格转换为小矩形 [x, y, width, height]
                obs_rect.append([j, i, 1, 1])

    # 更新Map对象的障碍物
    map_env.update(obs_rect=obs_rect)
    return map_env

def run_aco_planning(start, goal, grid_map):
    """运行ACO路径规划"""
    print(f"🐜 开始ACO规划: {start} -> {goal}")
    grid = create_grid_from_map(grid_map)

    # 转换坐标格式：(row, col) -> (x, y)
    start_xy = (start[1], start[0])
    goal_xy = (goal[1], goal[0])

    aco = ACO(start_xy, goal_xy, grid)
    _, path, _ = aco.plan()

    if path:
        # 转换回 (row, col) 格式
        path_rc = [(p[1], p[0]) for p in path]
        print(f"✅ ACO找到路径，长度: {len(path_rc)}")
        return path_rc
    else:
        print("❌ ACO未找到路径")
        return None

def run_pso_planning(start, goal, grid_map):
    """运行PSO路径规划"""
    print(f"🔮 开始PSO规划: {start} -> {goal}")
    grid = create_grid_from_map(grid_map)

    # 转换坐标格式：(row, col) -> (x, y)
    start_xy = (start[1], start[0])
    goal_xy = (goal[1], goal[0])

    pso = PSO(start_xy, goal_xy, grid)
    _, path, _ = pso.plan()

    if path:
        # 转换回 (row, col) 格式
        path_rc = [(p[1], p[0]) for p in path]
        print(f"✅ PSO找到路径，长度: {len(path_rc)}")
        return path_rc
    else:
        print("❌ PSO未找到路径")
        return None

def run_rrt_planning(start, goal, grid_map):
    """运行RRT路径规划"""
    print(f"🌳 开始RRT规划: {start} -> {goal}")
    map_env = create_map_from_grid(grid_map)

    # 转换坐标格式：(row, col) -> (x, y)
    start_xy = (start[1], start[0])
    goal_xy = (goal[1], goal[0])

    rrt = RRT(start_xy, goal_xy, map_env)
    _, path, _ = rrt.plan()

    if path:
        # 转换回 (row, col) 格式
        path_rc = [(p[1], p[0]) for p in path]
        print(f"✅ RRT找到路径，长度: {len(path_rc)}")
        return path_rc
    else:
        print("❌ RRT未找到路径")
        return None

def run_rrt_star_planning(start, goal, grid_map):
    """运行RRT*路径规划"""
    print(f"⭐ 开始RRT*规划: {start} -> {goal}")
    map_env = create_map_from_grid(grid_map)

    # 转换坐标格式：(row, col) -> (x, y)
    start_xy = (start[1], start[0])
    goal_xy = (goal[1], goal[0])

    rrt_star = RRTStar(start_xy, goal_xy, map_env)
    _, path, _ = rrt_star.plan()

    if path:
        # 转换回 (row, col) 格式
        path_rc = [(p[1], p[0]) for p in path]
        print(f"✅ RRT*找到路径，长度: {len(path_rc)}")
        return path_rc
    else:
        print("❌ RRT*未找到路径")
        return None

# ===================== 简化的DQN实现 =====================
class ClassicDQN:
    """简化的DQN路径规划器"""
    def __init__(self, start, goal, grid_map):
        self.start = start
        self.goal = goal
        self.grid_map = grid_map
        self.path = []

    def train(self, episodes=100):
        """模拟训练过程"""
        print(f"模拟DQN训练 {episodes} 轮...")

    def plan_path(self):
        """使用简单的贪心策略模拟DQN路径规划"""
        current = self.start
        path = [current]
        visited = set([current])

        max_steps = self.grid_map.shape[0] * self.grid_map.shape[1]

        for _ in range(max_steps):
            if current == self.goal:
                break

            # 获取可行的邻居
            neighbors = []
            for dx, dy in [(-1,0), (1,0), (0,-1), (0,1), (-1,-1), (-1,1), (1,-1), (1,1)]:
                new_pos = (current[0] + dx, current[1] + dy)
                if (0 <= new_pos[0] < self.grid_map.shape[0] and
                    0 <= new_pos[1] < self.grid_map.shape[1] and
                    self.grid_map[new_pos[0], new_pos[1]] == 0):
                    neighbors.append(new_pos)

            if not neighbors:
                break

            # 选择距离目标最近的邻居
            best_neighbor = min(neighbors,
                              key=lambda p: np.sqrt((p[0] - self.goal[0])**2 + (p[1] - self.goal[1])**2))

            path.append(best_neighbor)
            current = best_neighbor

            if current in visited:
                # 如果陷入循环，随机选择
                if len(neighbors) > 1:
                    current = random.choice([n for n in neighbors if n not in visited] or neighbors)
                    path.append(current)

            visited.add(current)

        return path

    def visualize_training(self):
        """可视化训练过程"""
        print("DQN训练完成")

# =============================================================================
# 分类 D: 基于势场的方法 (Potential Field Methods)
# =============================================================================
# 1. 势场规划方法（PotentialFieldPlanner）
# 描述：
#   - 利用人工势场模型，将目标设为吸引源、障碍物设为排斥源，通过势场梯度引导移动。
# 优点：
#   - 直观简单：算法易于理解和实现，实时性好。
#   - 计算效率高：在许多实际应用中能迅速计算出合适路径。
# 缺点：
#   - 局部极小值问题：容易陷入局部极小值，导致无法达到目标。
#   - 参数敏感：需要精细调节吸引和排斥参数以避免性能下降。
#from PotentialFieldPlanner import PotentialFieldPlanner
#from MOAStar import MOAStar

# =============================================================================
# 路径指标计算函数
def compute_euclidean_length(path):
    """计算路径的欧几里得长度"""
    if not path or len(path) < 2:
        return 0
    length = 0
    for i in range(len(path) - 1):
        p1, p2 = path[i], path[i+1]
        length += np.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
    return length

def compute_path_length(path):
    """计算路径长度（步数）"""
    return len(path) if path else 0

def compute_map_metric(path, risk_map):
    """计算路径在风险地图上的指标"""
    if not path:
        return 0, 0, 0

    risks = []
    for point in path:
        x, y = point[0], point[1]
        if 0 <= x < risk_map.shape[0] and 0 <= y < risk_map.shape[1]:
            risks.append(risk_map[x, y])

    if not risks:
        return 0, 0, 0

    total_risk = sum(risks)
    max_risk = max(risks)
    avg_risk = total_risk / len(risks)

    return total_risk, max_risk, avg_risk

def compute_pixel_length(path):
    """计算路径的像素长度"""
    return len(path) if path else 0

def compute_turn_count(path):
    """计算路径的转弯次数"""
    if not path or len(path) < 3:
        return 0

    turns = 0
    for i in range(1, len(path) - 1):
        p1, p2, p3 = path[i-1], path[i], path[i+1]

        # 计算两个向量
        v1 = (p2[0] - p1[0], p2[1] - p1[1])
        v2 = (p3[0] - p2[0], p3[1] - p2[1])

        # 如果方向改变，则计为一次转弯
        if v1 != v2:
            turns += 1

    return turns

def compute_smoothness(path):
    """计算路径的平滑度（角度变化总和）"""
    if not path or len(path) < 3:
        return 0

    angle_sum = 0
    for i in range(1, len(path) - 1):
        p1, p2, p3 = path[i-1], path[i], path[i+1]

        # 计算两个向量
        v1 = np.array([p2[0] - p1[0], p2[1] - p1[1]])
        v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])

        # 计算角度变化
        if np.linalg.norm(v1) > 0 and np.linalg.norm(v2) > 0:
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            cos_angle = np.clip(cos_angle, -1, 1)  # 防止数值误差
            angle = np.arccos(cos_angle)
            angle_sum += angle

    return angle_sum
# =============================================================================
# 绘制路径函数
def plot_path(grid, path, start, end, title="A* Pathfinding"):
    """
    绘制路径搜索结果

    参数：
      grid: 2D 数组或 numpy.array，表示网格地图。
      path: 路径列表，每个元素为 (x, y) 坐标。如果路径为空则不绘制路径。
      start: 起点坐标 (x, y)。
      end: 终点坐标 (x, y)。
      title: 图形标题（可选）。
    """
    plt.figure(figsize=(5, 5),dpi=500)
    # 显示网格图像，使用 origin='upper' 确保原点在左上角，与数组下标一致
    plt.imshow(grid, cmap='gray', origin='upper')
    # 如果有路径数据，则绘制路径（红色线条）
    if path:
        # 提取路径的 x 和 y 坐标
        path_x = [p[1] for p in path]
        path_y = [p[0] for p in path]
        plt.plot(path_x, path_y, color='red', linewidth=2, label='Path')
    # 绘制起点和终点
    plt.scatter(start[1], start[0], color='green', s=200, marker='o', label='Start')
    plt.scatter(end[1], end[0], color='blue', s=200, marker='x', label='End')
    plt.title(title)
    plt.legend()
    plt.grid(True)
    plt.show()
    
 # 在调用算法前添加环境检查
def validate_environment(ice_image, start, goal):
    # 检查起点终点是否可达
    if ice_image[start] != 0 or ice_image[goal] != 0:
        raise ValueError("起点或终点位于障碍物上")
    
    # 简单连通性检查
    tmp_env = ice_image.copy()
    queue = [start]
    tmp_env[start] = 2  # 标记已访问
    while queue:
        x, y = queue.pop(0)
        for dx, dy in [(-1,0),(1,0),(0,-1),(0,1)]:
            nx, ny = x+dx, y+dy
            if 0<=nx<tmp_env.shape[0] and 0<=ny<tmp_env.shape[1]:
                if tmp_env[nx, ny] == 0:
                    if (nx, ny) == goal:
                        return True
                    tmp_env[nx, ny] = 2
                    queue.append((nx, ny))
    return False 

# =============================================================================
# 参数设置：各项指标的权重（新目标函数版本）
# 测试主函数
#path_image=ice_image#原始
#path_image=weighted_image#膨胀
#path_image=rect_mask#方框掩膜
path_image=poly_mask#方框掩膜
# 保存为 Excel
# base_path = "C:/Users/<USER>/Desktop/ICE/lable"
# output_excel_path = os.path.join(base_path, f"{1}.xlsx")
# df = pd.DataFrame(path_image)
# os.makedirs(os.path.dirname(output_excel_path), exist_ok=True)
# df.to_excel(output_excel_path, index=False, header=False)
# print(f"处理完成，结果已保存至 {output_excel_path}")
# path_image=circle_mask#圆形掩膜

# 设置起点和终点（这里起点设为右下角，终点设为左上角）
start = (path_image.shape[0] - 1, path_image.shape[1] - 1)
end = (0, 0)

if not validate_environment(path_image, start, end):
    print("错误：起点和终点之间没有连通路径！")
    sys.exit(1)
    
from sklearn.preprocessing import MinMaxScaler
safety_map=density_map_sliding#安全性地图
risk_map1=safety_map
risk_map2=narrow_risk_map

# 安全地图标准化
scaler_safety = MinMaxScaler()
safety_flat = risk_map1.reshape(-1, 1)  # 展平为一维样本数组
safety_scaled = scaler_safety.fit_transform(safety_flat)
safety_scaled = safety_scaled.reshape(safety_map.shape)  # 恢复二维形状
safety_scaled[:] = 0 
# 风险地图标准化
scaler_risk = MinMaxScaler()
risk_flat = risk_map2.reshape(-1, 1)
risk_scaled = scaler_risk.fit_transform(risk_flat)
risk_scaled = risk_scaled.reshape(risk_map2.shape)

# print("【Dijkstra 算法 - 经典版本】开始路径规划...")
# # 设置对角线移动策略（此处选择总是允许对角线移动）
# diagonal = 1
# dijkstra_classic_path, runs = DijkstraClassic(path_image, start, end, diagonal_movement=diagonal)
# if dijkstra_classic_path:
#     print("找到路径，迭代次数：", runs)
#     print("步数：", compute_path_length(dijkstra_classic_path))
#     plot_path(gray_image.T, dijkstra_classic_path, start, end, title="Dijkstra Classic")
# else:
#     print("未找到路径！")
    
# print("【A* 算法 - 经典版本】开始路径规划...")
# # 设置对角线移动策略（此处选择总是允许对角线移动）
# diagonal = 1
# astar_classic_path, runs = AstarClassic(path_image, start, end, diagonal_movement=diagonal)
# if astar_classic_path:
#     print("找到路径，迭代次数：", runs)
#     print("步数：", compute_path_length(astar_classic_path))
#     plot_path(path_image.T, astar_classic_path, start, end, title="A* Classic")
# else:
#     print("未找到路径！")
    
# print("\n【Theta* 算法】开始路径规划...")
# theta_star_path = ThetastarClassic(path_image, start, end)
# if theta_star_path:
#     print("路径找到，步数:", compute_path_length(theta_star_path))
#     plot_path(path_image, theta_star_path, start, end, title="Theta* Path")
# else:
#     print("未找到路径!") 
    
print("\n运行多目标Theta*算法...")
# 导入修改后的类和函数
from Hierarchical_MOA_ThetaStar123 import HierarchicalMOAThetaStar, run_optimization
# 初始化规划器
planner = HierarchicalMOAThetaStar(path_image, risk_map1, risk_map2)
# --- 参数优化部分---
perform_optimization = False  # 设置为True执行参数优化 (会比较耗时)
N_TRIALS_OPTIMIZATION = 100   # Optuna 试验次数
best_params = None
if perform_optimization:
    print("\n--- 开始参数优化 ---")
    # run_optimization 内部会调用 objective, objective 又会调用 plan_path
    best_params = run_optimization(path_image, risk_map1, risk_map2, start, end, n_trials=N_TRIALS_OPTIMIZATION)
    print("--- 参数优化完成 ---")
    print("找到的最佳参数:", best_params)
else:
    print("\n--- 跳过参数优化，使用默认参数 ---")
    # 如果不优化，可以使用 planner.default_params 或 None
    best_params = None # 或者 planner.default_params
# --- 执行路径规划与比较 ---
print("\n--- 开始最终路径规划与基线比较 ---")
start_time = time.time()
results = planner.run_and_compare_baselines(start, end, optimize_first=True, params=best_params)
execution_time = time.time() - start_time
print(f"\n规划与比较总耗时: {execution_time:.2f} 秒")
# --- 结果处理与可视化 ---
if 'moa' in results and results['moa'].get('pareto_front'):
    pareto_front = results['moa']['pareto_front']
    print(f"\n规划完成，共找到 {len(pareto_front)} 条 Pareto 最优路径")
    # 你可以选择一条特定的路径来可视化，例如距离最短的
    if 'best_distance' in results['moa']:
        selected_path_info = results['moa']['best_distance']
        selected_path = selected_path_info['path']
        selected_objectives = selected_path_info['objectives']
        path_title = f"MOA Theta* - 最短距离路径 (目标: {selected_objectives})"
        print(f"\n选择 '最短距离' 路径进行展示:")
        print(f"  目标值: {selected_objectives}")
        path_len_geo = compute_euclidean_length(selected_path) # 计算几何长度
        print(f"  几何长度: {path_len_geo:.2f}")
        # 使用您的绘图函数
        plot_path(gray_image, selected_path, start, end, title=path_title)

    else:
         print("\n在MOA结果中未找到 'best_distance' 路径信息，展示第一条路径")
         # 回退到选择第一条 Pareto 最优路径
         selected_path_info = pareto_front[0]
         selected_path = selected_path_info[0]
         selected_objectives = selected_path_info[1]
         path_title = f"MOA Theta* - Pareto 路径 0 (目标: {selected_objectives})"
         print(f"\n选择第一条 Pareto 路径进行展示:")
         print(f"  目标值: {selected_objectives}")
         path_len_geo = compute_euclidean_length(selected_path) # 计算几何长度
         print(f"  几何长度: {path_len_geo:.2f}")
         plot_path(gray_image, selected_path, start, end, title=path_title)

    # 这里可以添加调用 planner.evaluate_dominance(results) 来打印支配关系总结
    print("\n--- 支配关系评估 ---")
    planner.evaluate_dominance(results)

elif 'moa' in results:
     print("\nMOA Theta* 规划完成，但未找到有效的 Pareto 最优路径。")
     # 仍然可以显示基线结果（如果存在）
     if 'theta' in results:
          print("\n显示经典 Theta* 基线路径作为参考:")
          baseline_path = results['theta']['path']
          baseline_objectives = results['theta']['objectives']
          path_title = f"经典 Theta* 路径 (目标: {baseline_objectives})"
          print(f"  目标值: {baseline_objectives}")
          path_len_geo = compute_euclidean_length(baseline_path) # 计算几何长度
          print(f"  几何长度: {path_len_geo:.2f}")
          plot_path(gray_image, baseline_path, start, end, title=path_title)
     else:
          print("也未能找到经典 Theta* 基线路径。")

else:
    print("\n规划执行出错或未能产生任何结果。")
    
    
moa_results=results['moa']
figure_image = path_image

plt.figure(figsize=(10, 10), dpi=1000)
# 1. Dijkstra算法
plt.subplot(2, 2, 1)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
dijkstra_path = results.get('dijkstra', {}).get('path')
if dijkstra_path:
    path_x = [p[1] for p in dijkstra_path]
    path_y = [p[0] for p in dijkstra_path]
    plt.plot(path_x, path_y, color='red', linewidth=2, label='Path')
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("Dijkstra", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=12)
plt.axis('off')
# 2. A*算法
plt.subplot(2, 2, 2)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
astar_path = results.get('astar', {}).get('path')
if astar_path:
    path_x = [p[1] for p in astar_path]
    path_y = [p[0] for p in astar_path]
    plt.plot(path_x, path_y, color='red', linewidth=2, label='Path')
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("A*", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=8)
plt.axis('off')
# 3. Theta*算法
plt.subplot(2, 2, 3)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
theta_path = results.get('theta', {}).get('path')
if theta_path:
    path_x = [p[1] for p in theta_path]
    path_y = [p[0] for p in theta_path]
    plt.plot(path_x, path_y, color='red', linewidth=2, label='Path')
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("Theta*", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=8)
plt.axis('off')
# 4. MOA-Theta*算法
plt.subplot(2, 2, 4)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
if selected_path:  # 使用上面选出的 MOA 最优路径
    path_x = [p[1] for p in selected_path]
    path_y = [p[0] for p in selected_path]
    plt.plot(path_x, path_y, color='red', linewidth=2, label='Path')
    
# # 绘制MOA不同最优目标的路径
# if 'best_distance' in moa_results:
#     path = moa_results['best_distance']['path']
#     xs = [p[1] for p in path]
#     ys = [p[0] for p in path]
#     plt.plot(xs, ys, color='red', linestyle='-', linewidth=4, marker='o', markersize=6, label='Path - Distance')  # 蓝色

# if 'best_max_risk1' in moa_results:
#     path = moa_results['best_max_risk1']['path']
#     xs = [p[1] for p in path]
#     ys = [p[0] for p in path]
#     plt.plot(xs, ys, color='#ff7f0e', linestyle='--', linewidth=2, marker='s', markersize=5, label='Path - Max Risk (Density)')  # 橙色

# if 'best_avg_risk1' in moa_results:
#     path = moa_results['best_avg_risk1']['path']
#     xs = [p[1] for p in path]
#     ys = [p[0] for p in path]
#     plt.plot(xs, ys, color='#2ca02c', linestyle='-.', linewidth=3, marker='^', markersize=3, label='Path - Avg Risk (Density)')  # 绿色

# if 'best_max_risk2' in moa_results:
#     path = moa_results['best_max_risk2']['path']
#     xs = [p[1] for p in path]
#     ys = [p[0] for p in path]
#     plt.plot(xs, ys, color='#d62728', linestyle='-', linewidth=3, marker='x', markersize=5, label='Path - Max Risk (Narrow)')  # 红色（细虚线，marker更明显）

# if 'best_avg_risk2' in moa_results:
#     path = moa_results['best_avg_risk2']['path']
#     xs = [p[1] for p in path]
#     ys = [p[0] for p in path]
#     plt.plot(xs, ys, color='#9467bd', linestyle='--', linewidth=2, marker='D', markersize=3, label='Path - Avg Risk (Narrow)')  # 紫色

     
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("HMOA-Theta*", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=8)
plt.axis('off')
plt.tight_layout()
plt.show()

# print("【BestFirst 算法 - 经典版本】开始路径规划...")
# # 设置对角线移动策略（此处选择总是允许对角线移动）
# diagonal = 3
# bestfirst_classic_path, runs = Best_FirstClassic(path_image, start, end, diagonal_movement=diagonal)
# if bestfirst_classic_path:
#     print("找到路径，迭代次数：", runs)
#     print("步数：", compute_path_length(bestfirst_classic_path))
#     plot_path(gray_image, bestfirst_classic_path, start, end, title="BestFirst Classic")
# else:
#     print("未找到路径！")
    
# print("【BFS 算法 - 经典版本】开始路径规划...")
# # 设置对角线移动策略（此处选择总是允许对角线移动）
# diagonal = 3
# bfs_classic_path, runs = BFS(path_image, start, end, diagonal_movement=diagonal)
# if bfs_classic_path:
#     print("找到路径，迭代次数：", runs)
#     print("步数：", compute_path_length(bfs_classic_path))
#     plot_path(gray_image, bfs_classic_path, start, end, title="BestFirst Classic")
# else:
#     print("未找到路径！")    
    
# print("\n【LPA* 算法】开始路径规划...")
# lpa_star = LPAstar(path_image, start, end)
# lpa_star_path = lpa_star.computeShortestPath()
# if lpa_star_path:
#     print("路径找到，步数:", compute_path_length(lpa_star_path))
#     plot_path(gray_image.T, lpa_star_path, start, end, title="Theta* Path")
# else:
#     print("未找到路径!")

print("\n【RRT 算法】开始路径规划...")
rrt_path = run_rrt_planning(start, end, path_image)
if rrt_path:
    print("路径找到")
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')

    # 绘制RRT路径
    path_array = np.array(rrt_path)
    plt.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=2, label='RRT Path')
    plt.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=3)

    plt.plot(start[1], start[0], 'go', markersize=10, label='Start')
    plt.plot(end[1], end[0], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('RRT 路径规划')
    plt.show()
else:
    print("未找到路径!")
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')
    plt.plot(start[1], start[0], 'go', markersize=10, label='Start')
    plt.plot(end[1], end[0], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('RRT（未找到路径）')
    plt.show()

print("\n【RRTstar 算法】开始路径规划...")
rrt_star_path = run_rrt_star_planning(start, end, path_image)
if rrt_star_path:
    print("路径找到")
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')

    # 绘制RRT*路径
    path_array = np.array(rrt_star_path)
    plt.plot(path_array[:, 1], path_array[:, 0], 'm-', linewidth=2, label='RRT* Path')
    plt.plot(path_array[:, 1], path_array[:, 0], 'mo', markersize=3)

    plt.plot(start[1], start[0], 'go', markersize=10, label='Start')
    plt.plot(end[1], end[0], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('RRT* 路径规划')
    plt.show()
else:
    print("未找到路径!")
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')
    plt.plot(start[1], start[0], 'go', markersize=10, label='Start')
    plt.plot(end[1], end[0], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('RRT*（未找到路径）')
    plt.show()

print("\n【蚁群算法】开始路径规划...")
print("使用改进的ACO算法...")

aco_path = run_aco_planning(start, end, path_image)
if aco_path:
    print("路径找到")
    # 可视化结果
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')

    # 绘制最优路径
    path_array = np.array(aco_path)
    plt.plot(path_array[:, 1], path_array[:, 0], 'g-', linewidth=2, label='ACO Path')
    plt.plot(path_array[:, 1], path_array[:, 0], 'go', markersize=3)
    plt.plot(start[1], start[0], 'bo', markersize=10, label='Start')
    plt.plot(end[1], end[0], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('ACO 路径规划')
    plt.show()
else:
    print("未找到可行路径")
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')
    plt.plot(start[1], start[0], 'go', markersize=10, label='Start')
    plt.plot(end[1], end[0], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('ACO（未找到路径）')
    plt.show()

print("\n【粒子群算法】开始路径规划...")
pso_path = run_pso_planning(start, end, path_image)

if pso_path:
    print(f"PSO找到路径，长度: {len(pso_path)}")
    # 可视化结果
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')

    # 绘制PSO路径
    path_array = np.array(pso_path)
    plt.plot(path_array[:, 1], path_array[:, 0], 'b-', linewidth=2, label='PSO Path')
    plt.plot(path_array[:, 1], path_array[:, 0], 'bo', markersize=3)

    # PSO路径已经是插值后的密集路径

    plt.plot(start[1], start[0], 'go', markersize=10, label='Start')
    plt.plot(end[1], end[0], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('PSO 路径规划')
    plt.show()
else:
    print("PSO未找到可行路径")
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')
    plt.plot(start[1], start[0], 'go', markersize=10, label='Start')
    plt.plot(end[1], end[0], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('PSO（未找到路径）')
    plt.show()

print("\n【RRT算法】开始路径规划...")
rrt_path = run_rrt_planning(start, end, path_image)

if rrt_path:
    print(f"RRT找到路径，长度: {len(rrt_path)}")
    # 可视化结果
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')

    # 绘制RRT路径
    path_array = np.array(rrt_path)
    plt.plot(path_array[:, 1], path_array[:, 0], 'g-', linewidth=2, label='RRT Path')
    plt.plot(path_array[:, 1], path_array[:, 0], 'go', markersize=3)

    plt.plot(start[1], start[0], 'bo', markersize=10, label='Start')
    plt.plot(end[1], end[0], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('RRT 路径规划')
    plt.show()
else:
    print("RRT未找到可行路径")
    rrt_path = None

print("\n【RRT*算法】开始路径规划...")
rrt_star_path = run_rrt_star_planning(start, end, path_image)

if rrt_star_path:
    print(f"RRT*找到路径，长度: {len(rrt_star_path)}")
    # 可视化结果
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')

    # 绘制RRT*路径
    path_array = np.array(rrt_star_path)
    plt.plot(path_array[:, 1], path_array[:, 0], 'm-', linewidth=2, label='RRT* Path')
    plt.plot(path_array[:, 1], path_array[:, 0], 'mo', markersize=3)

    plt.plot(start[1], start[0], 'bo', markersize=10, label='Start')
    plt.plot(end[1], end[0], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('RRT* 路径规划')
    plt.show()
else:
    print("RRT*未找到可行路径")
    rrt_star_path = None


print("\n【深度强化学习算法】开始路径规划...")
print("使用标准DQN进行路径规划")
# 创建DQN规划器
rl_planner = ClassicDQN(start, end, path_image)
# 训练DQN
print("开始训练DQN...")
rl_planner.train(episodes=100)
# 规划路径
rl_path = rl_planner.plan_path()
if rl_path:
    # 检查是否到达目标
    reached_goal = (rl_path[-1] == end)
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')
    # 绘制DQN路径
    path_array = np.array(rl_path)
    if reached_goal:
        print("DQN找到完整路径")
        plt.plot(path_array[:, 0], path_array[:, 1], 'g-', linewidth=2, label='DQN Complete Path')
        plt.plot(path_array[:, 0], path_array[:, 1], 'go', markersize=3)
        title = f'DQN完整路径 - 长度: {len(rl_path)}'
    else:
        print("DQN找到部分路径")
        plt.plot(path_array[:, 0], path_array[:, 1], 'orange', linewidth=2, linestyle='--', label='DQN Partial Path')
        plt.plot(path_array[:, 0], path_array[:, 1], 'o', color='orange', markersize=3)
        # 标记最终到达位置
        plt.plot(path_array[-1, 0], path_array[-1, 1], 'ro', markersize=8, label='Final Position')
        title = f'DQN部分路径 - 长度: {len(rl_path)} (未到达目标)'

    plt.plot(start[0], start[1], 'go', markersize=10, label='Start')
    plt.plot(end[0], end[1], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title(title)
    plt.show()
    # 显示训练统计
    rl_planner.visualize_training()
else:
    print("DQN未能进行有效探索")
    plt.figure(figsize=(8, 8))
    plt.imshow(gray_image, cmap='gray', origin='upper')

    plt.plot(start[0], start[1], 'go', markersize=10, label='Start')
    plt.plot(end[0], end[1], 'ro', markersize=10, label='Goal')
    plt.grid(True)
    plt.legend()
    plt.axis('equal')
    plt.title('DQN（未能探索）')
    plt.show()

    # 显示训练统计
    rl_planner.visualize_training()


# print("\n【势场法】开始路径规划...")
# pf = PotentialFieldPlanner(path_image, start, end)
# pf_path = pf.search()
# if pf_path:
#     print("路径找到，步数:", compute_path_length(pf_path))
#     plot_path(gray_image, pf_path, start, end, title="Potential Field Path")
# else:
#     print("未找到路径!")

plt.figure(figsize=(18, 12), dpi=1000)
# 1. Dijkstra算法
plt.subplot(3, 3, 1)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
dijkstra_path = results.get('dijkstra', {}).get('path')
if dijkstra_path:
    path_x = [p[1] for p in dijkstra_path]
    path_y = [p[0] for p in dijkstra_path]
    plt.plot(path_x, path_y, color='red', linewidth=2, label='Path')
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("Dijkstra", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=12)
plt.axis('off')
# 2. A*算法
plt.subplot(3, 3, 2)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
astar_path = results.get('astar', {}).get('path')
if astar_path:
    path_x = [p[1] for p in astar_path]
    path_y = [p[0] for p in astar_path]
    plt.plot(path_x, path_y, color='red', linewidth=2, label='Path')
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("A*", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=8)
plt.axis('off')
# 3. Theta*算法
plt.subplot(3, 3, 3)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
theta_path = results.get('theta', {}).get('path')
if theta_path:
    path_x = [p[1] for p in theta_path]
    path_y = [p[0] for p in theta_path]
    plt.plot(path_x, path_y, color='red', linewidth=2, label='Path')
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("Theta*", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=8)
plt.axis('off')
# 4. DQN算法
plt.subplot(3, 3, 4)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
if rl_path:  # 使用DQN路径
    path_x = [p[1] for p in rl_path]
    path_y = [p[0] for p in rl_path]

    # 检查是否到达目标
    reached_goal = (rl_path[-1] == end)
    if reached_goal:
        plt.plot(path_x, path_y, color='purple', linewidth=2, label='Complete Path')
    else:
        plt.plot(path_x, path_y, color='orange', linewidth=2, linestyle='--', label='Partial Path')
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("DQN", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=8)
plt.axis('off')
# 5. PSO算法
plt.subplot(3, 3, 5)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
if pso_path:  # 使用PSO路径
    path_x = [p[1] for p in pso_path]
    path_y = [p[0] for p in pso_path]
    plt.plot(path_x, path_y, color='blue', linewidth=2, label='Path')
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("PSO", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=8)
plt.axis('off')

# 6. RRT算法
plt.subplot(3, 3, 6)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
if rrt_path:  # 使用RRT路径
    path_x = [p[1] for p in rrt_path]
    path_y = [p[0] for p in rrt_path]
    plt.plot(path_x, path_y, color='green', linewidth=2, label='Path')
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("RRT", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=8)
plt.axis('off')

# 7. RRT*算法
plt.subplot(3, 3, 7)
plt.imshow(gray_image, cmap='gray', origin='upper')
plt.imshow(figure_image, cmap='gray', origin='upper', alpha=0.3)
if rrt_star_path:  # 使用RRT*路径
    path_x = [p[1] for p in rrt_star_path]
    path_y = [p[0] for p in rrt_star_path]
    plt.plot(path_x, path_y, color='magenta', linewidth=2, label='Path')
plt.scatter(start[1]-3, start[0]-3, color='green', s=100, marker='o', label='Start')
plt.scatter(end[1]+3, end[0]+3, color='yellow', s=100, marker='o', label='End')
plt.title("RRT*", fontsize=14, fontname='Times New Roman')
plt.legend(loc='upper right', fontsize=8)
plt.axis('off')

plt.tight_layout()
plt.show()





















# 对比各算法的路径指标
methods = {
    "Dijkstra": dijkstra_path,
    "A*": astar_path,
    "Theta*": theta_path,
    "DQN": rl_path,
    "PSO": pso_path,
    "RRT": rrt_path,
    "RRT*": rrt_star_path,
}

path_metrics_results = {} # Renamed to avoid conflict
print("\n--- 计算路径指标 ---")
for method, path_data in methods.items():
    actual_path = None
    objectives = None # 用于存储 MOA* 的目标值 (可选)
    # 检查输入数据是否是元组 (path, objectives)，如果是则提取路径
    if isinstance(path_data, tuple) and len(path_data) == 2 and isinstance(path_data[0], list):
        actual_path = path_data[0]
        objectives = path_data[1]
        print(f"处理 {method}: 识别为 (path, objectives) 元组。")
    elif isinstance(path_data, list):
        actual_path = path_data
        print(f"处理 {method}: 识别为路径列表。")
    else:
        print(f"处理 {method}: 无有效路径数据，跳过指标计算。")
        continue # 跳过这个方法
    if actual_path: # 确保路径非空
        try:
            # 使用导入的函数计算指标
            total_risk1, max_risk1, avg_risk1 = compute_map_metric(actual_path, risk_map1)
            total_risk2, max_risk2, avg_risk2 = compute_map_metric(actual_path, risk_map2)

            metrics_dict = {
                "像素长度": compute_pixel_length(actual_path),
                "欧氏长度": compute_euclidean_length(actual_path),
                "转弯次数": compute_turn_count(actual_path),
                "平滑度(角度和)": compute_smoothness(actual_path),
                "风险1-累计": total_risk1,
                "风险1-最大": max_risk1,
                "风险1-平均": avg_risk1,
                "风险2-累计": total_risk2,
                "风险2-最大": max_risk2,
                "风险2-平均": avg_risk2,
            }
            # 如果有来自MOA*的目标值，可以一起存储或打印对比
            if objectives is not None and len(objectives) == 5:
                 metrics_dict["目标-归一化距离"] = objectives[0]
                 metrics_dict["目标-最大风险1"] = objectives[1]
                 metrics_dict["目标-平均风险1"] = objectives[2]
                 metrics_dict["目标-最大风险2"] = objectives[3]
                 metrics_dict["目标-平均风险2"] = objectives[4]

            path_metrics_results[method] = metrics_dict
            print(f"为 {method} 计算指标完成。")
        except Exception as e:
            print(f"为 {method} 计算指标时出错: {e}")

# 打印统计结果
print("\n--- 路径指标对比 ---")
for method, metrics in path_metrics_results.items():
    print(f"\n【{method}】路径指标：")
    for key, value in metrics.items():
        # 格式化浮点数输出
        print(f"  {key}: {value:.3f}" if isinstance(value, (float, np.floating)) else f"  {key}: {value}")

