"""
测试python_motion_planning-master集成和导入修复
"""
import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加python_motion_planning-master到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'python_motion_planning-master', 'src'))

def test_import():
    """测试导入python_motion_planning模块"""
    print("🧪 测试python_motion_planning模块导入")
    print("=" * 50)
    
    try:
        from python_motion_planning.global_planner.evolutionary_search.aco import ACO
        print("✅ ACO导入成功")
    except ImportError as e:
        print(f"❌ ACO导入失败: {e}")
    
    try:
        from python_motion_planning.global_planner.evolutionary_search.pso import PSO
        print("✅ PSO导入成功")
    except ImportError as e:
        print(f"❌ PSO导入失败: {e}")
    
    try:
        from python_motion_planning.global_planner.sample_search.rrt import RRT
        print("✅ RRT导入成功")
    except ImportError as e:
        print(f"❌ RRT导入失败: {e}")
    
    try:
        from python_motion_planning.global_planner.sample_search.rrt_star import RRTStar
        print("✅ RRT*导入成功")
    except ImportError as e:
        print(f"❌ RRT*导入失败: {e}")
    
    try:
        from python_motion_planning.utils.environment.env import Env
        print("✅ Env导入成功")
    except ImportError as e:
        print(f"❌ Env导入失败: {e}")

def test_simple_planning():
    """测试简单路径规划"""
    print("\n🎯 测试简单路径规划")
    print("=" * 50)
    
    # 创建简单测试环境
    grid = np.zeros((20, 20))
    
    # 添加障碍物
    grid[5:15, 8] = 1  # 垂直墙
    grid[10, 5:15] = 1  # 水平墙
    grid[10, 6:8] = 0   # 在墙中间留个缺口
    
    start = (2, 2)
    goal = (17, 17)
    
    print(f"测试环境: {grid.shape}")
    print(f"起点: {start}, 终点: {goal}")
    print(f"障碍物数量: {np.sum(grid)}")
    
    # 可视化测试环境
    plt.figure(figsize=(8, 6))
    plt.imshow(grid, cmap='gray_r', origin='upper', alpha=0.7)
    plt.plot(start[1], start[0], 'go', markersize=12, label='Start')
    plt.plot(goal[1], goal[0], 'ro', markersize=12, label='Goal')
    plt.title('测试环境 - python_motion_planning集成')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
    
    try:
        from python_motion_planning.utils.environment.env import Env
        from python_motion_planning.global_planner.evolutionary_search.aco import ACO
        
        # 创建环境
        env = Env(grid)
        
        # 转换坐标格式：(row, col) -> (x, y)
        start_xy = (start[1], start[0])
        goal_xy = (goal[1], goal[0])
        
        # 测试ACO
        print("\n测试ACO算法...")
        aco = ACO(start_xy, goal_xy, env)
        path, _ = aco.run()
        
        if path:
            print(f"✅ ACO找到路径，长度: {len(path)}")
            # 转换回 (row, col) 格式
            path_rc = [(p[1], p[0]) for p in path]
            print(f"路径: {path_rc[:5]}...{path_rc[-5:]}")
        else:
            print("❌ ACO未找到路径")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_integration_status():
    """显示集成状态"""
    print("\n" + "="*60)
    print("📊 python_motion_planning-master 集成状态")
    print("="*60)
    
    print("\n🎯 集成方式:")
    print("✅ 直接导入python_motion_planning-master模块")
    print("✅ 使用适配器函数转换坐标格式")
    print("✅ 保持原始算法实现不变")
    
    print("\n🔧 适配器函数:")
    print("- run_aco_planning(): ACO路径规划")
    print("- run_pso_planning(): PSO路径规划") 
    print("- run_rrt_planning(): RRT路径规划")
    print("- run_rrt_star_planning(): RRT*路径规划")
    
    print("\n📁 文件结构:")
    print("主文件: 海冰分割20250722一审修改4.py")
    print("算法库: python_motion_planning-master/")
    print("测试文件: test_python_motion_planning_integration.py")
    
    print("\n🚀 使用方法:")
    print("1. 确保python_motion_planning-master文件夹在正确位置")
    print("2. 运行主文件进行完整测试")
    print("3. 算法会自动调用原始实现")
    
    print("\n💡 优势:")
    print("- 无需复制代码，直接使用原始实现")
    print("- 保持算法的完整性和正确性")
    print("- 便于维护和更新")
    print("- 支持所有原始算法参数")

if __name__ == "__main__":
    test_import()
    test_simple_planning()
    show_integration_status()
