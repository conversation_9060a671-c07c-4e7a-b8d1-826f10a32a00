import heapq
import numpy as np
import math
import matplotlib.pyplot as plt
from collections import OrderedDict
import matplotlib.cm as cm
import random
from typing import List, Union
import optuna
#import pygmo as pg # 导入 pygmo

# 导入基线算法用于比较
from AstarClassic import AstarClassic
from DijkstraClassic import DijkstraClassic
from ThetastarClassic import ThetastarClassic

# 基础函数
def heuristic(a: tuple, b: tuple) -> float:
    """改进的八方向启发式函数"""
    dx = abs(a[0]-b[0])
    dy = abs(a[1]-b[1])
    return (dx+dy) + (math.sqrt(2)-2)*min(dx,dy)

def distance(a: tuple, b: tuple) -> float:
    """计算欧几里得距离"""
    return math.hypot(a[0]-b[0], a[1]-b[1])

# 线可见性检查类
class LineOfSightCache:
    def __init__(self, ice_image, max_cache_size=10000):
        """初始化线可见性缓存
        
        Args:
            ice_image: 障碍物地图
            max_cache_size: 最大缓存项数量，防止内存溢出
        """
        self.ice_image = ice_image
        self.max_cache_size = max_cache_size
        self.cache = OrderedDict()  # 使用OrderedDict实现LRU缓存
        self.hits = 0
        self.misses = 0
    
    def has_line_of_sight(self, a: tuple, b: tuple, angle_constraint: float = None) -> bool:
        """使用缓存的线可见性检查，支持角度约束
        
        Args:
            a, b: 需要检查的两点
            angle_constraint: 角度约束(弧度)，如果设置，会检查转弯角度是否超过约束
            
        Returns:
            两点之间是否有线可见性
        """
        # 对称性：a->b 与 b->a 是等价的
        key = tuple(sorted([a, b]))
        
        # 角度约束检查使用不同的缓存键
        if angle_constraint is not None:
            key = (*key, angle_constraint)
            
        if key in self.cache:
            # 更新LRU顺序
            self.cache.move_to_end(key)
            self.hits += 1
            return self.cache[key]
        
        self.misses += 1
        
        # 首先检查直接视线
        basic_los = self._check_line_of_sight(a, b)
        
        # 如果没有角度约束或基本视线已经不通，直接返回结果
        if angle_constraint is None or not basic_los:
            result = basic_los
        else:
            # 否则，检查角度约束
            angle_ok = self._check_angle_constraint(a, b, angle_constraint)
            result = basic_los and angle_ok
        
        # 添加到缓存，如果缓存已满，删除最老的项
        if len(self.cache) >= self.max_cache_size:
            self.cache.popitem(last=False)
        
        self.cache[key] = result
        return result
    
    def _check_angle_constraint(self, a: tuple, b: tuple, max_angle: float) -> bool:
        """检查转弯角度是否在约束范围内"""
        # 此处实现角度约束检查
        # 简单实现：检查两点之间的直线与x轴的夹角是否小于max_angle
        if max_angle >= math.pi:  # 如果约束为180度或更大，总是满足
            return True
            
        dx = b[0] - a[0]
        dy = b[1] - a[1]
        
        # 计算方向角 (与x轴的夹角)
        angle = math.atan2(dy, dx)
        
        # 检查角度是否在约束范围内 (考虑周期性)
        return abs(angle) <= max_angle
    
    def check_turn_angle(self, prev: tuple, curr: tuple, next_pt: tuple, max_angle: float) -> bool:
        """检查三个连续点形成的转弯角度是否在约束范围内
        
        Args:
            prev: 前一个点
            curr: 当前点
            next_pt: 下一个点
            max_angle: 最大允许角度（弧度）
            
        Returns:
            转弯角度是否小于等于最大允许角度
        """
        if max_angle >= math.pi:  # 如果约束为180度或更大，总是满足
            return True
            
        # 计算向量
        v1 = (curr[0] - prev[0], curr[1] - prev[1])
        v2 = (next_pt[0] - curr[0], next_pt[1] - curr[1])
        
        # 检查零向量情况
        v1_mag = math.sqrt(v1[0]**2 + v1[1]**2)
        v2_mag = math.sqrt(v2[0]**2 + v2[1]**2)
        
        # 处理零向量情况
        if v1_mag < 1e-6 or v2_mag < 1e-6:
            return True  # 如果有一个向量接近零长度，视为直线，总是满足
        
        # 计算向量夹角的余弦值
        cos_angle = (v1[0]*v2[0] + v1[1]*v2[1]) / (v1_mag * v2_mag)
        
        # 避免数值误差
        cos_angle = max(-1.0, min(1.0, cos_angle))
        
        # 计算夹角（弧度）
        angle = math.acos(cos_angle)
        
        # 检查角度是否超过约束
        return angle <= max_angle
    
    def _check_line_of_sight(self, a: tuple, b: tuple) -> bool:
        """线可见性检查实现，使用增强的Bresenham算法"""
        # 使用超采样Bresenham以提高精度
        line = self._enhanced_bresenham_line(a, b)
        
        # 检查线上每个点
        for (x, y) in line:
            # 边界检查
            if x < 0 or y < 0 or x >= self.ice_image.shape[0] or y >= self.ice_image.shape[1]:
                return False
                
            # 障碍物检查 (冰块值为非0)
            if self.ice_image[x, y] != 0:
                return False
                
        return True
    
    def _enhanced_bresenham_line(self, start: tuple, end: tuple, supersample: int = 2) -> list:
        """增强的Bresenham算法，支持超采样以提高准确性
        
        Args:
            start, end: 起点和终点
            supersample: 超采样系数，越高精度越高但性能越低
            
        Returns:
            线路径上的点列表
        """
        x1, y1 = start
        x2, y2 = end
        
        # 计算基于实数的线路径点
        line_length = distance(start, end)
        if line_length < 1e-6:  # 避免除零错误
            return [start]
            
        # 采样点数量 = 线段长度 * 超采样系数
        num_points = max(2, int(line_length * supersample))
        
        # 使用线性插值生成更精确的点
        points = []
        for i in range(num_points):
            t = i / (num_points - 1)  # 参数 t 从 0 到 1
            x = round(x1 + t * (x2 - x1))
            y = round(y1 + t * (y2 - y1))
            point = (x, y)
            # 避免重复点
            if not points or point != points[-1]:
                points.append(point)
        
        return points 

# NSGA-III 算法实现
class NSGAIII:
    """改进的NSGA-III算法，专为多目标路径规划优化设计"""
    
    def __init__(self, num_objectives=5, num_reference_points=91, max_population_size=200):
        """初始化NSGA-III算法
        
        Args:
            num_objectives: 目标数量
            num_reference_points: 参考点数量
            max_population_size: 种群最大大小
        """
        self.num_objectives = num_objectives
        self.num_reference_points = num_reference_points
        self.max_population_size = max_population_size
        
        # 生成参考点
        self.reference_points = self._generate_structured_reference_points(num_objectives, num_reference_points)
        
        # 存储当前种群
        self.population = []  # 存储个体列表，每个个体是(路径ID, 路径, 目标值)的元组
        self.last_niche_counts = {} # 新增：存储上一轮选择的niche计数
    
    def _generate_structured_reference_points(self, num_objectives, num_points_approx):
        """生成结构化参考点 - 使用Das和Dennis方法的改进版本"""
        from itertools import combinations_with_replacement
        
        # 确定划分数
        H = 1
        while self._combination_count(num_objectives, H) < num_points_approx:
            H += 1
        
        # 生成主要参考点
        points = []
        for combo in combinations_with_replacement(range(H+1), num_objectives-1):
            point = [0] * num_objectives
            point[0] = H - sum(combo)
            for i, val in enumerate(combo):
                point[i+1] = val - (0 if i == 0 else combo[i-1])
            
            # 规范化到[0,1]区间
            normalized_point = [p/H for p in point]
            points.append(normalized_point)
        
        # 如果需要更多点，添加额外的内部参考点
        if len(points) < num_points_approx and num_objectives > 3:
            H_inside = H - 1
            if H_inside > 0:
                inside_points = []
                for combo in combinations_with_replacement(range(H_inside+1), num_objectives-1):
                    point = [0] * num_objectives
                    point[0] = H_inside - sum(combo)
                    for i, val in enumerate(combo):
                        point[i+1] = val - (0 if i == 0 else combo[i-1])
                    
                    # 规范化到[0,1]区间
                    normalized_point = [(p/H_inside) * 0.9 + 0.05 for p in point]  # 缩放到[0.05, 0.95]
                    inside_points.append(normalized_point)
                
                # 合并外部和内部参考点
                points.extend(inside_points)
        
        return np.array(points)
    
    def _combination_count(self, n, k):
        """计算组合数 C(n+k-1, k)"""
        from math import comb
        return comb(n+k-1, k)
    
    def _normalize_objectives(self, objectives):
        """归一化目标值到[0,1]区间"""
        min_values = np.min(objectives, axis=0)
        max_values = np.max(objectives, axis=0)
        
        # 避免除零错误
        ranges = np.maximum(1e-10, max_values - min_values)
        
        # 归一化
        normalized = (objectives - min_values) / ranges
        
        return normalized, min_values, ranges
    
    def _calculate_ideal_point(self, objectives):
        """计算理想点（每个目标的最小值）"""
        return np.min(objectives, axis=0)
    
    def _calculate_nadir_point(self, objectives, fronts):
        """计算反理想点（第一前沿每个目标的最大值）"""
        if not fronts or not fronts[0]:
            return np.max(objectives, axis=0)
        
        # 只考虑第一前沿的解
        first_front_objectives = objectives[fronts[0]]
        return np.max(first_front_objectives, axis=0)
    
    def _associate_to_reference_points(self, normalized_objectives):
        """将解关联到最近的参考点，使用垂直距离"""
        associations = {}  # 参考点索引 -> 关联解的列表
        distances = {}     # (解索引, 参考点索引) -> 距离
        
        for i, obj in enumerate(normalized_objectives):
            min_dist = float('inf')
            closest_rp = -1
            
            for j, rp in enumerate(self.reference_points):
                # 计算到参考线的垂直距离
                rp_norm = np.sqrt(np.sum(rp**2))
                if rp_norm < 1e-10:  # 避免除零错误
                    continue
                    
                # 计算在参考方向上的投影长度
                cosine = np.sum(obj * rp) / (np.sqrt(np.sum(obj**2)) * rp_norm)
                cosine = min(1.0, max(-1.0, cosine))  # 修正数值误差
                
                # 计算垂直距离
                dist = np.sqrt(np.sum(obj**2)) * np.sqrt(1 - cosine**2)
                
                distances[(i, j)] = dist
                
                if dist < min_dist:
                    min_dist = dist
                    closest_rp = j
            
            # 将解关联到最近的参考点
            if closest_rp not in associations:
                associations[closest_rp] = []
            associations[closest_rp].append(i)
        
        return associations, distances
    
    def _non_dominated_sort(self, population):
        """执行非支配排序，返回前沿列表"""
        objectives = np.array([ind[2] for ind in population])
        n = len(objectives)
        domination_count = [0] * n
        dominated_solutions = [[] for _ in range(n)]
        fronts = [[]]
        
        # 计算支配关系
        for i in range(n):
            for j in range(n):
                if i == j:
                    continue
                
                if self._dominates(objectives[i], objectives[j]):
                    dominated_solutions[i].append(j)
                elif self._dominates(objectives[j], objectives[i]):
                    domination_count[i] += 1
            
            # 将非支配解加入第一前沿
            if domination_count[i] == 0:
                fronts[0].append(i)
        
        # 修复：生成后续前沿
        front_idx = 0
        while front_idx < len(fronts) and fronts[front_idx]:
            next_front = []
            for solution_idx in fronts[front_idx]:
                for dominated_idx in dominated_solutions[solution_idx]:
                    domination_count[dominated_idx] -= 1
                    if domination_count[dominated_idx] == 0:
                        next_front.append(dominated_idx)
            
            if next_front:  # 只有当next_front非空时才添加新前沿
                fronts.append(next_front)
            
            front_idx += 1
        
        return fronts
    
    def _dominates(self, a, b):
        """判断解a是否支配解b（所有目标都不差，至少一个更好）"""
        better_in_any = False
        for i in range(len(a)):
            if a[i] > b[i]:  # 假设较小的值更好
                return False
            if a[i] < b[i]:
                better_in_any = True
        return better_in_any
    
    def select_best_members(self, combined_population):
        """使用NSGA-III机制选择最优的成员"""
        if len(combined_population) <= self.max_population_size:
            return combined_population
            
        # 提取目标值
        objectives = np.array([ind[2] for ind in combined_population])
        
        # 执行非支配排序
        fronts = self._non_dominated_sort(combined_population)
        
        # 选择最终种群
        next_population = []
        remaining = self.max_population_size
        front_idx = 0
        
        # 添加完整前沿直到达到人口上限
        while front_idx < len(fronts) and len(next_population) + len(fronts[front_idx]) <= self.max_population_size:
            for idx in fronts[front_idx]:
                next_population.append(combined_population[idx])
            remaining -= len(fronts[front_idx])
            front_idx += 1
        
        # 如果有足够的空间，直接返回
        if len(next_population) == self.max_population_size or front_idx == len(fronts):
            return next_population
        
        # 否则，我们需要从当前前沿中选择一部分解
        last_front = fronts[front_idx]
        
        # 如果剩余空间不足，直接随机选择
        if remaining <= 0 or not last_front:
            return next_population
            
        # 提取最后前沿的目标值
        last_front_objectives = objectives[last_front]
        
        # 规范化目标值
        ideal_point = self._calculate_ideal_point(objectives)
        nadir_point = self._calculate_nadir_point(objectives, fronts)
        
        # 转换到规范化空间
        translated_objectives = last_front_objectives - ideal_point
        
        # 避免除零错误
        scale = nadir_point - ideal_point
        scale[scale < 1e-10] = 1.0
        
        # 规范化
        normalized_objectives = translated_objectives / scale
        
        # 将解关联到参考点
        associations, distances = self._associate_to_reference_points(normalized_objectives)
        
        # 基于参考点选择剩余解
        selected_indices = []
        
        # 计算每个参考点的周围解密度 (小生境计数)
        niche_count = {rp: 0 for rp in range(len(self.reference_points))}
        # 先计算所有已选入 next_population 的解对 niche_count 的贡献
        # (这一步在原版NSGA-III标准中通常不做，但有助于更准确反映前沿密度)
        # pre_selected_indices = [idx for front in fronts[:front_idx] for idx in front]
        # if pre_selected_indices:
        #     pre_selected_objs = objectives[pre_selected_indices]
        #     # ... (需要规范化和关联)
            
        
        # 在选择last_front的过程中累加niche_count
        while len(selected_indices) < remaining:
            min_niche_value = float('inf')
            potential_rps = []

            # 找出当前niche_count最小的参考点(们)
            for rp_idx in range(len(self.reference_points)):
                 # 仅考虑有关联解且这些解在last_front中的参考点
                 associated_in_last_front = [idx for idx in associations.get(rp_idx, []) if last_front[idx] in last_front]
                 if associated_in_last_front:
                     current_niche_val = niche_count.get(rp_idx, 0)
                     if current_niche_val < min_niche_value:
                         min_niche_value = current_niche_val
                         potential_rps = [rp_idx]
                     elif current_niche_val == min_niche_value:
                         potential_rps.append(rp_idx)
            
            if not potential_rps:
                 # 如果没有可选的参考点（可能所有解都被选完了），退出
                 break
                 
            # 从niche_count最小的参考点中随机选一个
            selected_rp = random.choice(potential_rps)
            
            # 获取关联到这个参考点的、在last_front中且尚未被选中的解
            candidates_in_rp = [idx for idx in associations.get(selected_rp, []) 
                                  if last_front[idx] in last_front and idx not in selected_indices]
                                  
            if not candidates_in_rp:
                 # 如果这个rp没有可选的解了(可能已被其他rp选中)，继续下一轮循环
                 # 为避免死循环，将此rp的计数设为无穷大，或从potential_rps中移除
                 niche_count[selected_rp] = float('inf') # 防止下次再选它
                 continue

            # 选择策略：如果niche count为0，选距离最近的；否则，随机选一个
            chosen_sol_idx = -1
            if niche_count.get(selected_rp, 0) == 0:
                 min_dist_in_rp = float('inf')
                 for sol_idx in candidates_in_rp:
                     dist = distances.get((sol_idx, selected_rp), float('inf'))
                     if dist < min_dist_in_rp:
                         min_dist_in_rp = dist
                         chosen_sol_idx = sol_idx
            else:
                 chosen_sol_idx = random.choice(candidates_in_rp)

            if chosen_sol_idx != -1:
                 selected_indices.append(chosen_sol_idx)
                 niche_count[selected_rp] = niche_count.get(selected_rp, 0) + 1
            else:
                 # Fallback: 如果因某种原因没选出解，随机从未选中的里面选一个，防止卡死
                 remaining_indices = [i for i in range(len(last_front)) if i not in selected_indices]
                 if not remaining_indices:
                     break
                 random_idx = random.choice(remaining_indices)
                 selected_indices.append(random_idx)
                 # 更新这个解关联到的rp的niche_count (如果需要精确计算)
                 # ... (此处简化，不反向更新)

        # --- 选择结束 --- 
        # 存储计算出的 niche_count 以供外部使用
        self.last_niche_counts = niche_count 

        # 添加选中的解到下一代
        for idx in selected_indices:
            if idx < len(last_front):  # 确保索引有效
                next_population.append(combined_population[last_front[idx]])
        
        return next_population
    
    def evolve(self, population, crossover_rate=0.9, mutation_rate=0.1, ice_image=None, los_cache=None, 
               angle_constraint=None, risk_map1=None, risk_map2=None,
               smoothing_factor=0.5, smoothing_iterations=3, apply_smoothing=True):
        """使用遗传操作进化种群"""
        if not population:
            return population
            
        self.population = population
        
        # 选择父代
        parents = self._tournament_selection(population, len(population))
        
        # 生成后代
        offspring = []
        for i in range(0, len(parents), 2):
            if i + 1 < len(parents):
                if random.random() < crossover_rate:
                    try:
                        child1, child2 = self._crossover(parents[i], parents[i+1], 
                                                       ice_image, los_cache, angle_constraint)
                        # 确保返回的是有效个体 (元组)
                        if isinstance(child1, tuple) and len(child1) == 3:
                            offspring.append(child1)
                        else:
                            offspring.append(parents[i]) # 交叉失败保留父代1
                        if isinstance(child2, tuple) and len(child2) == 3:
                            offspring.append(child2)
                        else:
                            offspring.append(parents[i+1]) # 交叉失败保留父代2
                            
                    except Exception as e:
                        print(f"交叉操作出错: {e}")
                        # 出错时保留父代
                        offspring.append(parents[i])
                        offspring.append(parents[i+1])
                else:
                    offspring.append(parents[i])
                    offspring.append(parents[i+1])
            elif i < len(parents): # 处理奇数父代的情况
                 offspring.append(parents[i])
        
        # 变异
        mutated_offspring = []
        for ind in offspring:
            if random.random() < mutation_rate:
                try:
                    mutated_ind = self._mutate(ind, ice_image, los_cache, angle_constraint)
                    # 确保返回的是有效个体
                    if isinstance(mutated_ind, tuple) and len(mutated_ind) == 3:
                         mutated_offspring.append(mutated_ind)
                    else:
                        mutated_offspring.append(ind) # 变异失败保留原个体
                except Exception as e:
                    print(f"变异操作出错: {e}")
                    mutated_offspring.append(ind) # 出错时保留原始个体
            else:
                 mutated_offspring.append(ind)

        # 对后代应用平滑 (可选)
        smoothed_offspring = []
        if apply_smoothing:
            for ind in mutated_offspring:
                path_id, path, _ = ind
                if path and len(path) >= 2:
                    try:
                         smoothed_path = smooth_path_safely(path, ice_image, smoothing_factor, smoothing_iterations)
                         if smoothed_path and len(smoothed_path) >= 2:
                             smoothed_offspring.append((f"{path_id}_s", smoothed_path, None)) # 标记为平滑过，目标待计算
                         else:
                             smoothed_offspring.append(ind) # 平滑失败保留原个体
                    except Exception as e:
                        print(f"平滑操作出错: {e}")
                        smoothed_offspring.append(ind) # 出错保留原个体
                else:
                     smoothed_offspring.append(ind)
        else:
            smoothed_offspring = mutated_offspring
        
        # 评估新解的目标值
        valid_offspring = []
        for i in range(len(smoothed_offspring)):
            try:
                path_id, path, _ = smoothed_offspring[i] # 注意：目标值是None
                if path and len(path) >= 2 and risk_map1 is not None and risk_map2 is not None and los_cache is not None:
                    objectives = calculate_path_objectives(path, risk_map1, risk_map2, los_cache)
                    # 确保目标值有效
                    if all(math.isfinite(obj) for obj in objectives):
                        valid_offspring.append((path_id, path, objectives))
                    else:
                         print(f"跳过无效目标值路径: {path_id}")
                else:
                    # print(f"跳过无效或过短路径: {path_id}") # 可能产生过多日志
                    pass
            except Exception as e:
                print(f"目标计算出错: {e}, Path ID: {smoothed_offspring[i][0]}")
        
        # 合并父代和有效后代
        combined = population + valid_offspring
        
        # 选择下一代
        next_population = self.select_best_members(combined)
        
        return next_population
    
    def _tournament_selection(self, population, tournament_size, k=2):
        """锦标赛选择法选择父代"""
        if not population or tournament_size <= 0:
            return []
            
        selected = []
        for _ in range(tournament_size):
            # 随机选择k个个体
            if len(population) <= k:
                candidates = population
            else:
                candidates = random.sample(population, k)
            
            if not candidates:
                continue
                
            # 找出非支配的个体
            best = candidates[0]
            for candidate in candidates[1:]:
                if self._dominates(candidate[2], best[2]):
                    best = candidate
            
            selected.append(best)
        
        return selected
    
    def _crossover(self, parent1, parent2, ice_image, los_cache, angle_constraint):
        """执行路径交叉操作，保证生成有效路径"""
        path1, path2 = parent1[1], parent2[1]
        
        if len(path1) < 2 or len(path2) < 2:
            return parent1, parent2
        
        # 随机选择交叉点
        idx1 = random.randint(1, len(path1) - 1)
        idx2 = random.randint(1, len(path2) - 1)
        
        # 尝试创建新路径
        new_path1 = path1[:idx1] + path2[idx2:]
        new_path2 = path2[:idx2] + path1[idx1:]
        
        # 安全性检查：确保路径可行
        new_path1 = self._ensure_path_safety(new_path1, ice_image, los_cache, angle_constraint)
        new_path2 = self._ensure_path_safety(new_path2, ice_image, los_cache, angle_constraint)
        
        # 如果路径无效，返回原始父代
        if not new_path1 or not new_path2:
            return parent1, parent2
        
        # 创建新个体
        child1 = (f"{parent1[0]}_c1", new_path1, None)  # 目标值稍后计算
        child2 = (f"{parent2[0]}_c2", new_path2, None)
        
        return child1, child2
    
    def _mutate(self, individual, ice_image, los_cache, angle_constraint):
        """对路径执行变异操作，保证安全性"""
        path = individual[1].copy()
        if len(path) < 3:
            return individual
        
        # 选择变异类型
        mutation_type = random.choice(["add", "remove", "replace", "smooth"])
        
        if mutation_type == "add" and len(path) < 50:
            # 添加新点
            idx = random.randint(1, len(path) - 1)
            prev_pt = path[idx - 1]
            next_pt = path[idx]
            
            # 在两点之间随机选择一个点
            t = random.random()
            new_pt = (
                int(prev_pt[0] + t * (next_pt[0] - prev_pt[0])), 
                int(prev_pt[1] + t * (next_pt[1] - prev_pt[1]))
            )
            
            # 边界检查
            height, width = ice_image.shape
            new_pt = (
                max(0, min(height - 1, new_pt[0])),
                max(0, min(width - 1, new_pt[1]))
            )
            
            # 障碍物检查
            if ice_image[new_pt[0], new_pt[1]] == 0:
                if (los_cache.has_line_of_sight(prev_pt, new_pt) and 
                    los_cache.has_line_of_sight(new_pt, next_pt)):
                    path.insert(idx, new_pt)
        
        elif mutation_type == "remove" and len(path) > 3:
            # 移除一个点
            idx = random.randint(1, len(path) - 2)
            prev_pt = path[idx - 1]
            next_pt = path[idx + 1]
            
            # 检查删除点后是否仍可行
            if los_cache.has_line_of_sight(prev_pt, next_pt):
                path.pop(idx)
        
        elif mutation_type == "replace":
            # 替换一个点
            idx = random.randint(1, len(path) - 2)
            prev_pt = path[idx - 1]
            next_pt = path[idx + 1]
            
            # 在周围随机选择一个点
            radius = random.randint(1, 3)
            angle = random.random() * 2 * math.pi
            new_pt = (
                int(path[idx][0] + radius * math.cos(angle)),
                int(path[idx][1] + radius * math.sin(angle))
            )
            
            # 边界检查
            height, width = ice_image.shape
            new_pt = (
                max(0, min(height - 1, new_pt[0])),
                max(0, min(width - 1, new_pt[1]))
            )
            
            # 障碍物和可见性检查
            if (ice_image[new_pt[0], new_pt[1]] == 0 and
                los_cache.has_line_of_sight(prev_pt, new_pt) and
                los_cache.has_line_of_sight(new_pt, next_pt)):
                path[idx] = new_pt
        
        elif mutation_type == "smooth":
            # 平滑路径
            path = smooth_path_safely(path, ice_image, smoothing_factor=0.3, iterations=1)
        
        # 确保变异后的路径仍然安全
        safe_path = self._ensure_path_safety(path, ice_image, los_cache, angle_constraint)
        
        if safe_path:
            return (f"{individual[0]}_m", safe_path, None)  # 目标值稍后计算
        else:
            return individual
    
    def _ensure_path_safety(self, path, ice_image, los_cache, angle_constraint):
        """确保路径安全可行，修复不可行路径"""
        if not path or len(path) < 2:
            return None
            
        fixed_path = [path[0]]  # 起点必须保留
        
        for i in range(1, len(path)):
            current = path[i]
            previous = fixed_path[-1]
            
            # 边界检查
            height, width = ice_image.shape
            if not (0 <= current[0] < height and 0 <= current[1] < width):
                continue
                
            # 障碍物检查
            if ice_image[current[0], current[1]] != 0:
                continue
                
            # 可见性检查
            if not los_cache.has_line_of_sight(previous, current):
                # 如果当前点与上一个安全点不可见，尝试从上上个安全点连接
                # 这有助于跳过一些不必要的拐点
                if len(fixed_path) > 1:
                    prev_prev = fixed_path[-2]
                    if los_cache.has_line_of_sight(prev_prev, current):
                        # 检查角度约束
                        angle_ok = True
                        if angle_constraint is not None and len(fixed_path) > 2:
                             angle_ok = los_cache.check_turn_angle(fixed_path[-3], prev_prev, current, angle_constraint)
                        
                        if angle_ok:
                            fixed_path[-1] = current # 替换上一个安全点
                    continue
                else:
                     continue
            
            # 角度约束检查
            angle_ok = True
            if angle_constraint is not None and len(fixed_path) > 1:
                angle_ok = los_cache.check_turn_angle(fixed_path[-2], previous, current, angle_constraint)
            
            if angle_ok:
                fixed_path.append(current)

        # 确保终点有效且连接安全
        final_goal = path[-1]
        # 检查原始终点是否有效
        goal_is_valid = (0 <= final_goal[0] < ice_image.shape[0] and 
                         0 <= final_goal[1] < ice_image.shape[1] and 
                         ice_image[final_goal[0], final_goal[1]] == 0)

        if fixed_path[-1] != final_goal and goal_is_valid:
            if los_cache.has_line_of_sight(fixed_path[-1], final_goal):
                # 检查角度
                angle_ok = True
                if angle_constraint is not None and len(fixed_path) > 1:
                     angle_ok = los_cache.check_turn_angle(fixed_path[-2], fixed_path[-1], final_goal, angle_constraint)
                if angle_ok:
                    fixed_path.append(final_goal)
            # 如果直接连接终点不可行，尝试从fixed_path中找到最后一个能安全连接到终点的点
            else:
                for j in range(len(fixed_path) - 2, -1, -1):
                    if los_cache.has_line_of_sight(fixed_path[j], final_goal):
                        # 检查角度
                        angle_ok = True
                        if angle_constraint is not None and j > 0:
                             angle_ok = los_cache.check_turn_angle(fixed_path[j-1], fixed_path[j], final_goal, angle_constraint)
                        if angle_ok:
                           # 截断到j，然后添加终点
                           fixed_path = fixed_path[:j+1]
                           fixed_path.append(final_goal)
                           break
        elif fixed_path[-1] == final_goal:
            pass # 终点已经是最后一个点
        else: # fixed_path的最后一个点不是终点，且终点无效或无法连接
            return None # 无法修复到有效终点，路径无效

        # 路径必须至少有起点和终点
        if len(fixed_path) < 2 or fixed_path[0] != path[0] or fixed_path[-1] != path[-1]:
            return None
            
        return fixed_path

# 路径目标函数计算
def calculate_path_objectives(path, risk_map1, risk_map2, los_cache):
    """计算路径的多个目标值，返回五维目标向量
    [路径长度，最大风险1，平均风险1，最大风险2，平均风险2]
    """
    if not path or len(path) < 2:
        return [float('inf'), float('inf'), float('inf'), float('inf'), float('inf')]

    path_length = 0.0
    risk1_max = 0.0
    risk1_sum = 0.0
    risk2_max = 0.0
    risk2_sum = 0.0
    total_points = 0
    
    for i in range(len(path) - 1):
        a, b = path[i], path[i + 1]
        segment_length = distance(a, b)
        path_length += segment_length
        
        # 使用超采样Bresenham获取更密集的点
        line = los_cache._enhanced_bresenham_line(a, b, supersample=5)
        
        # 计算每个点的风险值
        for pt in line:
            if 0 <= pt[0] < risk_map1.shape[0] and 0 <= pt[1] < risk_map1.shape[1]:
                # 风险值
                risk1_val = risk_map1[pt[0], pt[1]]
                risk2_val = risk_map2[pt[0], pt[1]]
                
                # 累加风险值和最大值
                risk1_sum += risk1_val
                risk2_sum += risk2_val
                
                risk1_max = max(risk1_max, risk1_val)
                risk2_max = max(risk2_max, risk2_val)
                
                total_points += 1
    
    # 归一化距离代价 (相对于直线距离的比例)
    straight_line_dist = distance(path[0], path[-1])
    if straight_line_dist > 1e-6:
        normalized_path_length = path_length / straight_line_dist
    else:
        # 起点终点重合的情况，使用路径长度本身
        normalized_path_length = path_length
    
    # 计算平均风险值
    risk1_avg = risk1_sum / max(1, total_points)
    risk2_avg = risk2_sum / max(1, total_points)
        
    # 返回五维目标向量：[距离，最大风险1，平均风险1，最大风险2，平均风险2]
    return [normalized_path_length, risk1_max, risk1_avg, risk2_max, risk2_avg]

# 安全的路径平滑函数
def smooth_path_safely(path, ice_image, smoothing_factor=0.5, iterations=3):
    """使用插值和弛豫技术平滑路径，同时保证安全性
    
    Args:
        path: 原始路径
        ice_image: 障碍物地图
        smoothing_factor: 平滑因子 (0-1)
        iterations: 平滑迭代次数
        
    Returns:
        平滑后的路径
    """
    if len(path) <= 2:
        return path
    
    # 复制原始路径避免修改输入
    smoothed = path.copy()
    los_checker = LineOfSightCache(ice_image)
    
    # 多次迭代平滑
    for _ in range(iterations):
        # 从第二个点到倒数第二个点进行平滑
        for i in range(1, len(smoothed) - 1):
            # 计算相邻点的平均位置
            avg_x = (smoothed[i-1][0] + smoothed[i+1][0]) / 2
            avg_y = (smoothed[i-1][1] + smoothed[i+1][1]) / 2
            
            # 新位置 = 当前位置 + 平滑因子 * (平均位置 - 当前位置)
            new_x = int(smoothed[i][0] + smoothing_factor * (avg_x - smoothed[i][0]))
            new_y = int(smoothed[i][1] + smoothing_factor * (avg_y - smoothed[i][1]))
            new_point = (new_x, new_y)
            
            # 安全性检查：确保新位置在地图范围内且不是障碍
            if (0 <= new_x < ice_image.shape[0] and 
                0 <= new_y < ice_image.shape[1] and 
                ice_image[new_x, new_y] == 0):
                
                # 安全性检查：确保到相邻点的连线不穿过障碍物
                if (los_checker.has_line_of_sight(smoothed[i-1], new_point) and 
                    los_checker.has_line_of_sight(new_point, smoothed[i+1])):
                    smoothed[i] = new_point
    
    return smoothed 

# 任意角度邻居采样函数
def sample_neighbors(current: tuple, radius: float = 3.0, num_samples: int = 16) -> list:
    """采样任意角度的邻居节点
    
    Args:
        current: 当前节点坐标
        radius: 采样半径，可以是变量
        num_samples: 采样点数量
        
    Returns:
        采样得到的邻居节点列表
    """
    # 使用集合存储邻居，避免重复检查
    neighbors_set = set()
    
    # 固定采样点 - 8个基本方向
    for dr in [-1, 0, 1]:
        for dc in [-1, 0, 1]:
            if dr == 0 and dc == 0:
                continue
            # 计算单位向量
            d = math.sqrt(dr*dr + dc*dc)
            unit_dr = dr / d
            unit_dc = dc / d
            # 添加固定距离的采样点
            neighbor = (current[0] + int(unit_dr * radius), 
                        current[1] + int(unit_dc * radius))
            neighbors_set.add(neighbor)
    
    # 随机采样更多方向的点
    angles = np.linspace(0, 2*np.pi, num_samples, endpoint=False)
    for angle in angles:
        dr = math.sin(angle) * radius
        dc = math.cos(angle) * radius
        neighbor = (current[0] + int(dr), current[1] + int(dc))
        neighbors_set.add(neighbor)  # 集合自动去重
    
    # 移除当前节点自身（如果存在）
    if current in neighbors_set:
        neighbors_set.remove(current)
    
    # 转换为列表返回
    return list(neighbors_set)

# 自适应采样半径函数
def adaptive_sampling_radius(current: tuple, goal: tuple, obstacles_density: float, 
                            base_radius: float = 3.0, 
                            distance_factor_scale: float = 30.0, 
                            density_factor_scale: float = 0.7, 
                            min_radius: float = 1.0, 
                            max_radius: float = 5.0) -> float:
    """根据环境和目标距离调整采样半径
    
    Args:
        current: 当前节点
        goal: 目标节点
        obstacles_density: 障碍物密度 (0-1之间)
        base_radius: 基础采样半径
        distance_factor_scale: 距离因子比例
        density_factor_scale: 密度因子比例
        min_radius: 最小采样半径
        max_radius: 最大采样半径
        
    Returns:
        适应性采样半径
    """
    # 距离因子：接近目标时减小半径实现精细搜索
    dist_to_goal = distance(current, goal)
    distance_factor = min(1.0, dist_to_goal / distance_factor_scale)  # 标准化距离因子
    
    # 障碍物密度因子：障碍物多时减小半径
    density_factor = 1.0 - density_factor_scale * obstacles_density
    
    # 计算最终半径 (范围min_radius-max_radius)
    final_radius = max(min_radius, min(max_radius, base_radius * distance_factor * density_factor))
    
    return final_radius

# 基础Theta*算法实现
def basic_thetastar(ice_image: np.ndarray, start: tuple, goal: tuple, 
                    risk_map1: np.ndarray = None, risk_map2: np.ndarray = None,
                    distance_weight: float = 1.0,
                    risk1_weight: float = 1.0,
                    risk2_weight: float = 1.0,
                    angle_constraint: float = None,
                    max_iterations: int = 10000,
                    cache_size: int = 10000,
                    sampling_radius: float = 3.0,
                    num_samples: int = 16):
    """基础的Theta*算法实现，用于生成单个路径
    
    Args:
        ice_image: 障碍物地图
        start: 起点坐标
        goal: 终点坐标
        risk_map1, risk_map2: 风险地图
        distance_weight: 距离权重
        risk1_weight: 风险1权重
        risk2_weight: 风险2权重
        angle_constraint: 角度约束(弧度)
        max_iterations: 最大迭代次数
        cache_size: 缓存大小
        sampling_radius: 采样半径
        num_samples: 采样点数量
        
    Returns:
        (路径, 路径ID, 目标值)
    """
    # 初始化风险图
    if risk_map1 is None:
        risk_map1 = np.zeros_like(ice_image, dtype=float)
    if risk_map2 is None:
        risk_map2 = np.zeros_like(ice_image, dtype=float)
    
    # 初始化线可见性缓存
    los_cache = LineOfSightCache(ice_image, max_cache_size=cache_size)
    
    # 计算障碍物密度，用于自适应采样
    non_zero_count = np.count_nonzero(ice_image)
    obstacles_density = non_zero_count / ice_image.size if ice_image.size > 0 else 0
    
    # 初始化搜索
    open_set = []
    closed_set = set()
    parent = {}
    g_score = {}
    g_score[start] = 0.0
    parent[start] = None
    
    # 使用加权和作为单目标搜索的f值
    f_score = heuristic(start, goal) * distance_weight
    heapq.heappush(open_set, (f_score, 0, start))  # (f值, 计数器, 节点)
    
    # Theta*不再需要key_points，使用parent指针重建路径
    
    # 用于生成唯一ID的计数器
    counter = 1
    
    # 搜索迭代
    iterations = 0
    while open_set and iterations < max_iterations:
        iterations += 1
        
        # 取出f值最小的节点
        _, _, current = heapq.heappop(open_set)
        
        # 如果已处理过，跳过
        if current in closed_set:
            continue
            
        # 添加到已处理集合
        closed_set.add(current)
        
        # 如果到达目标
        if current == goal:
            # 正确重建Theta*路径：使用parent指针回溯
            path = []
            node = current
            while node is not None:
                path.append(node)
                node = parent[node]
            path.reverse()

            # 平滑路径
            path = smooth_path_safely(path, ice_image)

            # 计算目标值
            objectives = calculate_path_objectives(path, risk_map1, risk_map2, los_cache)

            # 生成路径ID
            path_id = f"path_{hash(tuple(map(tuple, path))) % 10000}"

            return path, path_id, objectives
        
        # 采样邻居节点
        radius = adaptive_sampling_radius(
            current, goal, obstacles_density,
            base_radius=sampling_radius,
            min_radius=1.0,
            max_radius=5.0
        )
        neighbors = sample_neighbors(current, radius=radius, num_samples=num_samples)
        
        # 扩展邻居节点
        for neighbor in neighbors:
            # 跳过无效邻居
            if not (0 <= neighbor[0] < ice_image.shape[0] and 0 <= neighbor[1] < ice_image.shape[1]):
                continue
            if ice_image[neighbor[0], neighbor[1]] != 0 or neighbor in closed_set:
                continue
            
            # Theta*核心：检查是否可以通过视线内祖父节点直接连接
            if parent[current] is not None and los_cache.has_line_of_sight(parent[current], neighbor):
                # 检查角度约束（简化版本，不依赖key_points）
                angle_ok = True
                if (angle_constraint is not None and
                    parent[current] is not None and
                    parent[parent[current]] is not None):
                    # 使用祖父节点进行角度检查
                    grandparent = parent[parent[current]]
                    angle_ok = los_cache.check_turn_angle(grandparent, parent[current], neighbor, angle_constraint)

                if angle_ok:
                    # 直接连接祖父节点
                    parent_node = parent[current]
                    new_g = g_score[parent_node] + distance(parent_node, neighbor)
                else:
                    # 不满足角度约束，使用普通A*连接
                    parent_node = current
                    new_g = g_score[current] + distance(current, neighbor)
            else:
                # 正常A*连接
                parent_node = current
                new_g = g_score[current] + distance(current, neighbor)
            
            # 计算风险代价
            if 0 <= neighbor[0] < risk_map1.shape[0] and 0 <= neighbor[1] < risk_map1.shape[1]:
                risk1_value = risk_map1[neighbor[0], neighbor[1]]
                risk2_value = risk_map2[neighbor[0], neighbor[1]]
            else:
                risk1_value = 0.0
                risk2_value = 0.0
            
            # 如果找到更好的路径或者节点未访问过
            if neighbor not in g_score or new_g < g_score[neighbor]:
                # 更新路径
                g_score[neighbor] = new_g
                parent[neighbor] = parent_node
                
                # 计算启发式f值（Theta*标准做法）
                h_value = heuristic(neighbor, goal)
                # 对于多目标优化，使用加权组合作为单目标搜索指导
                # 但主要还是基于距离，风险作为辅助
                f_value = new_g + h_value + (risk1_value * risk1_weight + risk2_value * risk2_weight) * 0.1
                
                # 添加到开放集
                heapq.heappush(open_set, (f_value, counter, neighbor))
                counter += 1
    
    # 如果没有找到路径，返回None
    return None, None, None 

# 分层架构的MOA Theta*算法
class HierarchicalMOAThetaStar:
    """迭代分层架构的多目标Theta*算法 (带反馈机制)
    
    宏观迭代：
        1. 分析当前Pareto前沿，识别稀疏区域。
        2. 生成针对性权重以探索稀疏区域。
        3. 使用针对性权重 + 多样化权重生成基础路径。
        4. 使用NSGA-III优化合并后的种群。
    """
    
    def __init__(self, ice_image, risk_map1=None, risk_map2=None, 
                 # 添加可选参数以覆盖默认的NSGA-III设置
                 nsga3_pop_size=50, nsga3_ref_points=105):
        """初始化算法
        
        Args:
            ice_image: 障碍物地图
            risk_map1, risk_map2: 风险地图
            nsga3_pop_size: NSGA-III 种群大小
            nsga3_ref_points: NSGA-III 参考点数量
        """
        self.ice_image = ice_image
        self.risk_map1 = risk_map1 if risk_map1 is not None else np.zeros_like(ice_image, dtype=float)
        self.risk_map2 = risk_map2 if risk_map2 is not None else np.zeros_like(ice_image, dtype=float)
        
        # 线可见性缓存
        self.los_cache = LineOfSightCache(ice_image, max_cache_size=20000) # 增加缓存
        
        # 障碍物密度
        non_zero_count = np.count_nonzero(ice_image)
        self.obstacles_density = non_zero_count / ice_image.size if ice_image.size > 0 else 0
        
        # NSGA-III算法 (使用传入或默认的参数)
        self.nsga3 = NSGAIII(num_objectives=5, 
                             num_reference_points=nsga3_ref_points, 
                             max_population_size=nsga3_pop_size)
        
        # 默认参数
        self.default_params = {
            'angle_constraint': math.pi / 4,  # 放宽角度约束
            'max_iterations': 20000,        # 增加基础Theta*迭代
            'cache_size': 20000,
            'sampling_radius': 3.0,        # <-- 新增默认值
            'num_samples': 20,           # 增加采样点数
            'nsga3_pop_size': nsga3_pop_size, # <-- 新增默认值 (来自构造函数)
            # 'nsga3_ref_points': nsga3_ref_points, # 参考点数量通常不优化，保持构造函数传入
            'max_generations': 50,        # 增加NSGA-III代数
            'crossover_rate': 0.9,
            'mutation_rate': 0.15,         # 略微增加变异率
            'num_initial_paths_diverse': 10, # 用于多样性权重的数量
            'num_initial_paths_targeted': 5, # 用于针对性权重的数量
            'smoothing_factor': 0.5,
            'smoothing_iterations': 3,
            'max_macro_iterations': 3 # 新增：宏观迭代次数
        }
        self.current_pareto_front = [] # 用于存储跨宏观迭代的解
    
    def generate_diverse_weights(self, num_weights=10):
        """生成多样化的权重组合
        
        Args:
            num_weights: 权重组合的数量
            
        Returns:
            权重组合列表，每个元素为(distance_weight, risk1_weight, risk2_weight)
        """
        weights = []
        
        # 固定权重组合 - 强调不同目标
        fixed_weights = [
            (1.0, 0.1, 0.1),  # 强调距离
            (0.1, 1.0, 0.1),  # 强调风险1
            (0.1, 0.1, 1.0),  # 强调风险2
            (1.0, 1.0, 0.1),  # 平衡距离和风险1
            (1.0, 0.1, 1.0),  # 平衡距离和风险2
            (0.1, 1.0, 1.0),  # 平衡两种风险
            (1.0, 1.0, 1.0)   # 平衡所有因素
        ]
        weights.extend(fixed_weights)
        
        # 随机生成其他权重组合
        remaining = max(0, num_weights - len(fixed_weights))
        for _ in range(remaining):
            # 随机生成三个权重并归一化
            w1 = random.random()
            w2 = random.random()
            w3 = random.random()
            
            # 确保权重总和为1
            total = w1 + w2 + w3
            w1 /= total
            w2 /= total
            w3 /= total
            
            # 缩放权重到合适范围
            w1 *= 3.0
            w2 *= 3.0
            w3 *= 3.0
            
            weights.append((w1, w2, w3))
        
        return weights
    
    def analyze_pareto_front(self, num_targets=5):
        """分析Pareto前沿，找出稀疏区域对应的参考点 (基于NSGA-III小生境计数)
        
        Args:
            num_targets: 希望找到的目标稀疏区域数量
        
        Returns:
            参考点索引列表，代表稀疏区域
        """
        if not hasattr(self.nsga3, 'last_niche_counts') or not self.nsga3.last_niche_counts:
            print("警告: NSGA-III尚未计算小生境计数，无法分析稀疏区域。返回空列表。")
            return [] # NSGA-III 可能还没运行过选择步骤

        # 对小生境计数按计数值升序排序
        # niche_counts 格式: {rp_index: count}
        sorted_niches = sorted(self.nsga3.last_niche_counts.items(), key=lambda item: item[1])
        
        # 选取计数最少的 num_targets 个参考点
        sparse_rp_indices = [rp_idx for rp_idx, count in sorted_niches[:num_targets]]
        
        if not sparse_rp_indices:
            # 如果所有参考点计数都一样（例如初始阶段），随机选几个
            print("警告: 小生境计数均匀，随机选择目标区域。")
            num_available_rps = len(self.nsga3.reference_points)
            if num_available_rps > 0:
                return random.sample(range(num_available_rps), min(num_targets, num_available_rps))
            else:
                return []
                
        return sparse_rp_indices

    def generate_targeted_weights(self, targets: Union[List[int], List[List[float]]]):
        """根据目标区域（参考点索引或目标向量）生成针对性权重
        
        Args:
            targets: 参考点索引列表 或 目标向量列表
            
        Returns:
            权重组合列表 (distance_weight, risk1_weight, risk2_weight)
        """
        targeted_weights = []
        epsilon = 1e-6
        
        if not targets:
            return []
            
        # 判断输入类型
        is_rp_indices = isinstance(targets[0], int)
        
        for target in targets:
            if is_rp_indices:
                # 输入是参考点索引
                rp_index = target
                if rp_index >= len(self.nsga3.reference_points):
                    print(f"警告: 无效的参考点索引 {rp_index}，跳过")
                    continue
                # 获取参考点向量 (假设 reference_points 是 num_objectives 维)
                target_vec = self.nsga3.reference_points[rp_index]
            else:
                # 输入是目标向量
                target_vec = target
            
            if len(target_vec) != self.nsga3.num_objectives:
                 print(f"警告: 目标向量维度 {len(target_vec)} 与预期 {self.nsga3.num_objectives} 不符，跳过")
                 continue
                 
            # 标准方法：权重与参考点/目标向量分量成正比
            # 我们只用前3个目标来生成用于basic_thetastar的3个权重
            # 目标顺序: [距离, 最大风险1, 平均风险1, 最大风险2, 平均风险2]
            # basic_thetastar 权重顺序: (distance, risk1, risk2)
            
            # 归一化目标向量（可选，但有助于平衡不同尺度的目标）
            # target_sum = sum(target_vec)
            # if target_sum < epsilon: target_sum = epsilon
            # normalized_vec = [v / target_sum for v in target_vec]
            # 使用原始向量值似乎更直接反映方向
            normalized_vec = target_vec
            
            # 距离权重 = 目标向量中距离相关分量的值 (第0个)
            # 风险1权重 = 目标向量中风险1相关分量的值 (第1, 2个，取最大或平均)
            # 风险2权重 = 目标向量中风险2相关分量的值 (第3, 4个，取最大或平均)
            
            w_dist = max(epsilon, normalized_vec[0])  # 距离权重
            w_risk1 = max(epsilon, max(normalized_vec[1], normalized_vec[2])) # 风险1权重 (取最大风险分量)
            w_risk2 = max(epsilon, max(normalized_vec[3], normalized_vec[4])) # 风险2权重 (取最大风险分量)
            
            # 归一化这三个权重，使其和为1（或者一个固定值如3）
            total_w = w_dist + w_risk1 + w_risk2
            if total_w < epsilon: continue # 避免除零
                
            scale_factor = 3.0 # 使权重和约为3，与多样化权重范围类似
            final_w_dist = (w_dist / total_w) * scale_factor
            final_w_risk1 = (w_risk1 / total_w) * scale_factor
            final_w_risk2 = (w_risk2 / total_w) * scale_factor

            targeted_weights.append((final_w_dist, final_w_risk1, final_w_risk2))
        
        return targeted_weights

    def generate_base_paths_with_weights(self, start, goal, weights, params):
        """使用给定的权重组合生成基础路径"""
        base_paths = []
        successful_paths = 0
        for i, (d_weight, r1_weight, r2_weight) in enumerate(weights):
            print(f"  生成基础路径 {i+1}/{len(weights)}，权重：D={d_weight:.2f}, R1={r1_weight:.2f}, R2={r2_weight:.2f}")
            
            try:
                path, path_id, objectives = basic_thetastar(
                    self.ice_image, start, goal, 
                    self.risk_map1, self.risk_map2,
                    distance_weight=d_weight,
                    risk1_weight=r1_weight,
                    risk2_weight=r2_weight,
                    angle_constraint=params['angle_constraint'],
                    max_iterations=params['max_iterations'],
                    cache_size=params['cache_size'],
                    sampling_radius=params['sampling_radius'],
                    num_samples=params['num_samples']
                )
                
                if path is not None:
                    if len(path) >= 2:
                        smoothed_path = smooth_path_safely(
                            path, self.ice_image,
                            smoothing_factor=params['smoothing_factor'],
                            iterations=params['smoothing_iterations'])
                        
                        objectives = calculate_path_objectives(
                            smoothed_path, self.risk_map1, self.risk_map2, self.los_cache)
                        
                        if all(math.isfinite(obj) for obj in objectives):
                             base_paths.append((path_id, smoothed_path, objectives))
                             successful_paths += 1
                             # print(f"    路径生成成功，目标值={objectives}") # 减少日志
                        # else: print("    计算的目标值无效（含inf），跳过")
                    # else: print("    生成的路径无效，跳过")
                # else: print("    无法找到路径")
            except Exception as e:
                print(f"    基础路径生成过程中出错: {e}")
        
        print(f"  成功生成 {successful_paths} 条有效路径")
        return base_paths

    def plan_path(self, start, goal, params=None):
        """执行迭代分层路径规划 (带反馈和基线引导)"""
        current_params = self.default_params.copy()
        if params:
            current_params.update(params)
            
        print("\n使用的参数:")
        for key, val in current_params.items():
            print(f"  {key}: {val}")
            
        # --- 首先运行一次基线算法获取参考解 ---
        print("\n=== 预先运行基线算法获取参考 ===")
        grid_list = self.ice_image.tolist()
        baseline_paths = {} # 存储基线路径 (alg -> path)
        baseline_objectives = {} # 存储基线目标 (alg -> objectives)
        
        # Dijkstra
        try:
            # Dijkstra 输入是 (col, row)
            dijkstra_path_raw, _ = DijkstraClassic(grid_list, (start[1], start[0]), (goal[1], goal[0]), diagonal_movement=1)
            if dijkstra_path_raw:
                dijkstra_path_np = [(p[1], p[0]) for p in dijkstra_path_raw] # 转回 (row, col)
                obj = calculate_path_objectives(dijkstra_path_np, self.risk_map1, self.risk_map2, self.los_cache)
                if all(math.isfinite(o) for o in obj):
                    baseline_paths['dijkstra'] = dijkstra_path_np
                    baseline_objectives['dijkstra'] = obj
                    print(f"Dijkstra 参考解: 目标={obj}")
        except Exception as e: print(f"预运行 Dijkstra 出错: {e}")

        # A*
        try:
            # A* 输入是 (col, row)
            astar_path_raw, _ = AstarClassic(grid_list, (start[1], start[0]), (goal[1], goal[0]), diagonal_movement=1)
            if astar_path_raw:
                astar_path_np = [(p[1], p[0]) for p in astar_path_raw] # 转回 (row, col)
                obj = calculate_path_objectives(astar_path_np, self.risk_map1, self.risk_map2, self.los_cache)
                if all(math.isfinite(o) for o in obj):
                    baseline_paths['astar'] = astar_path_np
                    baseline_objectives['astar'] = obj
                    print(f"A* 参考解: 目标={obj}")
        except Exception as e: print(f"预运行 A* 出错: {e}")

        # Theta*
        try:
            # Theta* 输入是 (row, col)
            theta_path_raw = ThetastarClassic(self.ice_image, start, goal)
            if theta_path_raw:
                 # 输出也是 (row, col)，无需转换
                 obj = calculate_path_objectives(theta_path_raw, self.risk_map1, self.risk_map2, self.los_cache)
                 if all(math.isfinite(o) for o in obj):
                     baseline_paths['theta'] = theta_path_raw
                     baseline_objectives['theta'] = obj
                     print(f"Theta* 参考解: 目标={obj}")
        except Exception as e: print(f"预运行 Theta* 出错: {e}")
        # --- 基线运行结束 ---

        # 初始化总体种群 (用于NSGA-III)
        current_population = []
        all_generated_paths = {} # 用于去重 (path_tuple -> objectives)

        # --- 宏观迭代开始 ---
        for macro_iter in range(current_params['max_macro_iterations']):
            print(f"\n--- 宏观迭代 {macro_iter + 1}/{current_params['max_macro_iterations']} ---")
            
            # --- 1. 将基线解注入当前种群 (确保只在第一次迭代注入) ---
            if macro_iter == 0:
                 num_baseline_added = 0
                 for alg, path in baseline_paths.items():
                     objectives = baseline_objectives[alg]
                     path_tuple = tuple(map(tuple, path))
                     if path_tuple not in all_generated_paths:
                         path_id = f"baseline_{alg}_inj"
                         all_generated_paths[path_tuple] = objectives
                         current_population.append((path_id, path, objectives))
                         num_baseline_added += 1
                 if num_baseline_added > 0:
                     print(f"已将 {num_baseline_added} 个基线解注入初始种群")

            # --- 2. 分析当前Pareto前沿 (来自上一轮NSGA-III的结果) ---
            print("分析 Pareto 前沿以寻找稀疏区域...")
            num_targeted_total = current_params['num_initial_paths_targeted']
            num_target_sparse = max(1, num_targeted_total // 2) # 至少分配1个给稀疏区
            # analyze_pareto_front 现在返回参考点索引列表
            sparse_rp_indices = self.analyze_pareto_front(
                num_targets=num_target_sparse 
            )
            print(f"识别到 {len(sparse_rp_indices)} 个稀疏区域对应的参考点索引")
            
            # --- 3. 生成针对性权重 (稀疏区 + 基线引导) ---
            print("生成针对性权重 (稀疏区 + 基线引导)...")
            # 权重来自稀疏区分析 (传入参考点索引)
            targeted_weights = self.generate_targeted_weights(sparse_rp_indices)
            # 权重来自基线目标引导 (使用剩余的目标路径数额)
            num_target_baseline = num_targeted_total - len(targeted_weights)
            if num_target_baseline > 0 and baseline_objectives:
                 baseline_target_vectors = list(baseline_objectives.values())
                 # generate_targeted_weights 可以直接处理目标向量列表
                 weights_from_baseline = self.generate_targeted_weights(baseline_target_vectors)
                 # 取所需数量的权重
                 targeted_weights.extend(weights_from_baseline[:num_target_baseline])
            print(f"生成了 {len(targeted_weights)} 组针对性权重")
            
            # --- 4. 生成多样化权重 ---
            print("生成多样化权重...")
            diverse_weights = self.generate_diverse_weights(current_params['num_initial_paths_diverse'])
            print(f"生成了 {len(diverse_weights)} 组多样化权重")

            # --- 5. 使用所有权重生成基础路径 ---
            print("使用权重生成新的基础路径...")
            new_base_paths = []
            if targeted_weights:
                new_base_paths.extend(self.generate_base_paths_with_weights(start, goal, targeted_weights, current_params))
            if diverse_weights:
                 new_base_paths.extend(self.generate_base_paths_with_weights(start, goal, diverse_weights, current_params))

            # --- 6. 去重并合并到种群 ---
            num_added = 0
            for path_id, path, objectives in new_base_paths:
                path_tuple = tuple(map(tuple, path))
                if path_tuple not in all_generated_paths:
                     all_generated_paths[path_tuple] = objectives
                     current_population.append((path_id, path, objectives))
                     num_added += 1
                         
            print(f"添加了 {num_added} 条新的、不重复的基础路径到种群")
            print(f"当前总种群大小 (待优化): {len(current_population)}")

            # 如果种群为空，无法继续
            if not current_population:
                print("错误：种群为空，无法进行 NSGA-III 优化。提前终止。")
                break
                
            # --- 7. 使用NSGA-III优化合并后的种群 ---
            print("执行 NSGA-III 优化...")
            # 预选逻辑保持不变
            if len(current_population) > self.nsga3.max_population_size * 2:
                 print(f"种群过大({len(current_population)})，进行预选...", end='')
                 fronts = self.nsga3._non_dominated_sort(current_population)
                 selected_pop = []
                 target_size = self.nsga3.max_population_size * 2
                 for front in fronts:
                     if not front: continue
                     if len(selected_pop) + len(front) <= target_size:
                         selected_pop.extend([current_population[i] for i in front])
                     else:
                         needed = target_size - len(selected_pop)
                         if needed > 0:
                             # 优先保留注入的基线解 (如果它们在前沿中)
                             baseline_indices_in_front = {idx for idx in front if current_population[idx][0].startswith('baseline_')}
                             other_indices_in_front = [idx for idx in front if idx not in baseline_indices_in_front]
                             
                             selected_baselines = list(baseline_indices_in_front)[:needed]
                             selected_pop.extend([current_population[i] for i in selected_baselines])
                             
                             remaining_needed = needed - len(selected_baselines)
                             if remaining_needed > 0:
                                 selected_others = random.sample(other_indices_in_front, min(remaining_needed, len(other_indices_in_front)))
                                 selected_pop.extend([current_population[i] for i in selected_others])
                         break
                 # 如果预选后仍然为空（理论上不应发生），保留原始种群的一部分
                 if not selected_pop and current_population:
                     selected_pop = random.sample(current_population, min(len(current_population), target_size))
                 current_population = selected_pop
                 print(f" 预选后大小: {len(current_population)}")

            optimized_population_tuples = self.optimize_paths(current_population, current_params)
            print(f"NSGA-III 优化完成，得到 {len(optimized_population_tuples)} 条 Pareto 最优路径")
            
            # 更新当前最优解集 (用于下一轮分析和最终结果)
            self.current_pareto_front = optimized_population_tuples # optimize_paths 已返回 (path, objectives)
            # 更新 current_population 为优化后的结果，用于下一宏观迭代的合并
            current_population = [(f"gen{macro_iter+1}_{i}", path, objectives) 
                                for i, (path, objectives) in enumerate(self.current_pareto_front)]
            all_generated_paths = {tuple(map(tuple, p)): o for _, p, o in current_population} # 更新去重字典

        # --- 宏观迭代结束 ---
        print(f"\n迭代分层规划完成，最终找到 {len(self.current_pareto_front)} 条 Pareto 最优路径")
        
        # --- 构建最终结果字典，包含MOA结果和预计算的基线结果 ---
        final_results_dict = {
             'moa': {'pareto_front': self.current_pareto_front}
        }
        # 添加预计算的基线结果
        for alg, path in baseline_paths.items():
            final_results_dict[alg] = {
                'path': path,
                'objectives': baseline_objectives[alg],
                'time': -1 # 标记时间为预计算
            }
        # 在moa结果中找出最优项，方便比较
        if self.current_pareto_front:
             try:
                 best_distance_idx = min(range(len(self.current_pareto_front)), key=lambda i: self.current_pareto_front[i][1][0])
                 best_max_risk1_idx = min(range(len(self.current_pareto_front)), key=lambda i: self.current_pareto_front[i][1][1])
                 best_avg_risk1_idx = min(range(len(self.current_pareto_front)), key=lambda i: self.current_pareto_front[i][1][2])
                 best_max_risk2_idx = min(range(len(self.current_pareto_front)), key=lambda i: self.current_pareto_front[i][1][3])
                 best_avg_risk2_idx = min(range(len(self.current_pareto_front)), key=lambda i: self.current_pareto_front[i][1][4])
                 
                 final_results_dict['moa']['best_distance'] = {'path': self.current_pareto_front[best_distance_idx][0], 'objectives': self.current_pareto_front[best_distance_idx][1]}
                 final_results_dict['moa']['best_max_risk1'] = {'path': self.current_pareto_front[best_max_risk1_idx][0], 'objectives': self.current_pareto_front[best_max_risk1_idx][1]}
                 final_results_dict['moa']['best_avg_risk1'] = {'path': self.current_pareto_front[best_avg_risk1_idx][0], 'objectives': self.current_pareto_front[best_avg_risk1_idx][1]}
                 final_results_dict['moa']['best_max_risk2'] = {'path': self.current_pareto_front[best_max_risk2_idx][0], 'objectives': self.current_pareto_front[best_max_risk2_idx][1]}
                 final_results_dict['moa']['best_avg_risk2'] = {'path': self.current_pareto_front[best_avg_risk2_idx][0], 'objectives': self.current_pareto_front[best_avg_risk2_idx][1]}
             except ValueError: # 处理空pareto前沿的情况
                  print("警告：Pareto前沿为空，无法确定最佳解")
                  pass 

        return final_results_dict

    def optimize_paths(self, population_to_optimize, params):
        """调用 NSGA-III 进行多目标优化 (单次宏观迭代内的优化)"""
        if not population_to_optimize:
            return []
            
        current_pop = population_to_optimize
        for gen in range(params['max_generations']):
            print(f"  NSGA-III 第 {gen+1}/{params['max_generations']} 代...", end='\r')
            current_pop = self.nsga3.evolve(
                current_pop, 
                crossover_rate=params['crossover_rate'],
                mutation_rate=params['mutation_rate'],
                ice_image=self.ice_image,
                los_cache=self.los_cache,
                angle_constraint=params['angle_constraint'],
                risk_map1=self.risk_map1,
                risk_map2=self.risk_map2,
                smoothing_factor=params['smoothing_factor'],
                smoothing_iterations=params['smoothing_iterations'],
                apply_smoothing=True
            )
        print(" " * 50, end='\r') # 清除最后一行
        
        # NSGA-III evolve 返回的是下一代种群，已经是选出的 Pareto 最优集
        # 但为保险起见，再做一次去重（基于路径本身）
        unique_paths_data = []
        seen_path_tuples = set()
        for ind in current_pop:
            path_id, path, objectives = ind
            if path:
                path_tuple = tuple(map(tuple, path))
                if path_tuple not in seen_path_tuples:
                    seen_path_tuples.add(path_tuple)
                    unique_paths_data.append((path, objectives)) # 返回 (path, objectives)
            
        return unique_paths_data

    def visualize_paths(self, paths, start, goal, show_all=False):
        """可视化规划的路径
        
        Args:
            paths: 路径列表，每个元素为(路径, 目标值)
            start: 起点坐标
            goal: 终点坐标
            show_all: 是否显示所有路径，如果为False只显示前5条
        """
        # 设置matplotlib支持中文显示
        try:
            plt.rcParams['font.sans-serif'] = ['SimHei'] # 尝试使用 SimHei
            plt.rcParams['axes.unicode_minus'] = False
        except Exception as e:
            print(f"设置中文字体失败: {e}，可能需要安装SimHei字体。")
        
        plt.figure(figsize=(12, 10))
        
        # 绘制障碍物地图
        plt.imshow(self.ice_image, cmap='binary', origin='upper')
        
        # 绘制起点和终点
        plt.scatter(start[1], start[0], c='green', s=100, marker='*', label='起点')
        plt.scatter(goal[1], goal[0], c='red', s=100, marker='*', label='终点')
        
        # 选择要显示的路径数量
        if not show_all and len(paths) > 5:
            display_paths = paths[:5]
        else:
            display_paths = paths
        
        # 绘制路径
        colors = cm.rainbow(np.linspace(0, 1, len(display_paths)))
        
        for i, ((path, objectives), color) in enumerate(zip(display_paths, colors)):
            # 提取路径坐标
            xs = [p[1] for p in path]
            ys = [p[0] for p in path]
            
            # 路径标签
            label = f'路径 {i+1}: 距离={objectives[0]:.2f}, 最大风险1={objectives[1]:.2f}, 平均风险1={objectives[2]:.2f}'
            
            # 绘制路径
            plt.plot(xs, ys, color=color, linewidth=2, marker='.', markersize=3, label=label)
        
        plt.title('迭代分层MOA Theta*规划的Pareto最优路径')
        plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=1, fontsize='small')
        plt.tight_layout()
        plt.show()
        
        # 单独绘制风险地图
        if np.any(self.risk_map1) or np.any(self.risk_map2):
            fig, axes = plt.subplots(1, 2, figsize=(16, 8))
            
            # 风险地图1
            im1 = axes[0].imshow(self.risk_map1, cmap='hot', origin='upper')
            axes[0].set_title('风险地图1')
            plt.colorbar(im1, ax=axes[0])
            
            # 风险地图2
            im2 = axes[1].imshow(self.risk_map2, cmap='hot', origin='upper')
            axes[1].set_title('风险地图2')
            plt.colorbar(im2, ax=axes[1])
            
            plt.tight_layout()
            plt.show()

    def run_and_compare_baselines(self, start, goal, optimize_first=True, params=None):
        """运行基线算法并与多目标优化结果进行比较
        
        Args:
            start: 起点坐标
            goal: 终点坐标
            optimize_first: 是否先运行多目标优化再比较
            params: 多目标优化参数
            
        Returns:
            比较结果字典 (包含基线和MOA结果)
        """
        # 如果需要先优化，或者还没有优化结果，则运行 plan_path
        if optimize_first or not self.current_pareto_front:
            print("\n=== 运行迭代分层多目标优化 (包含基线预计算) ===")
            # plan_path 内部已包含基线计算和多目标优化，并返回包含所有结果的字典
            results = self.plan_path(start, goal, params)
        else:
            # 如果已经有优化结果，直接构建结果字典
            print("\n=== 使用现有优化结果进行比较 ===")
            results = {'moa': {'pareto_front': self.current_pareto_front}}
            # 需要重新运行基线算法或从缓存获取
            grid_list = self.ice_image.tolist()
            # 运行Dijkstra
            try:
                # Dijkstra 输入是 (col, row)
                dijkstra_path_raw, _ = DijkstraClassic(grid_list, (start[1], start[0]), (goal[1], goal[0]), diagonal_movement=1)
                if dijkstra_path_raw:
                    dijkstra_path_np = [(p[1], p[0]) for p in dijkstra_path_raw] # 转回 (row, col)
                    dijkstra_objectives = calculate_path_objectives(dijkstra_path_np, self.risk_map1, self.risk_map2, self.los_cache)
                    results['dijkstra'] = {'path': dijkstra_path_np, 'objectives': dijkstra_objectives, 'time': -1}
            except Exception as e: print(f"比较时运行Dijkstra出错: {e}")
            # 运行A*
            try:
                astar_path, _ = AstarClassic(grid_list, start, goal, diagonal_movement=1)
                if astar_path:
                    astar_path_np = [(p[1], p[0]) for p in astar_path]
                    astar_objectives = calculate_path_objectives(astar_path_np, self.risk_map1, self.risk_map2, self.los_cache)
                    results['astar'] = {'path': astar_path_np, 'objectives': astar_objectives, 'time': -1}
            except Exception as e: print(f"比较时运行A*出错: {e}")
            # 运行Theta*
            try:
                theta_path = ThetastarClassic(self.ice_image, start, goal)
                if theta_path:
                    theta_objectives = calculate_path_objectives(theta_path, self.risk_map1, self.risk_map2, self.los_cache)
                    results['theta'] = {'path': theta_path, 'objectives': theta_objectives, 'time': -1}
            except Exception as e: print(f"比较时运行Theta*出错: {e}")
            
            # 为MOA结果补充 best_... 字段
            if self.current_pareto_front:
                 best_distance_idx = min(range(len(self.current_pareto_front)), key=lambda i: self.current_pareto_front[i][1][0])
                 best_max_risk1_idx = min(range(len(self.current_pareto_front)), key=lambda i: self.current_pareto_front[i][1][1])
                 best_avg_risk1_idx = min(range(len(self.current_pareto_front)), key=lambda i: self.current_pareto_front[i][1][2])
                 best_max_risk2_idx = min(range(len(self.current_pareto_front)), key=lambda i: self.current_pareto_front[i][1][3])
                 best_avg_risk2_idx = min(range(len(self.current_pareto_front)), key=lambda i: self.current_pareto_front[i][1][4])
                 
                 results['moa']['best_distance'] = {'path': self.current_pareto_front[best_distance_idx][0], 'objectives': self.current_pareto_front[best_distance_idx][1]}
                 results['moa']['best_max_risk1'] = {'path': self.current_pareto_front[best_max_risk1_idx][0], 'objectives': self.current_pareto_front[best_max_risk1_idx][1]}
                 results['moa']['best_avg_risk1'] = {'path': self.current_pareto_front[best_avg_risk1_idx][0], 'objectives': self.current_pareto_front[best_avg_risk1_idx][1]}
                 results['moa']['best_max_risk2'] = {'path': self.current_pareto_front[best_max_risk2_idx][0], 'objectives': self.current_pareto_front[best_max_risk2_idx][1]}
                 results['moa']['best_avg_risk2'] = {'path': self.current_pareto_front[best_avg_risk2_idx][0], 'objectives': self.current_pareto_front[best_avg_risk2_idx][1]}
            
        # 比较和展示结果
        self.compare_and_visualize_results(results, start, goal)
        
        return results

    def compare_and_visualize_results(self, results, start, goal):
        """比较和可视化基线算法与多目标优化结果"""
        if not results:
            print("没有可比较的结果")
            return
            
        print("\n=== 比较结果 ===")
        
        # 设置matplotlib支持中文显示
        try:
            plt.rcParams['font.sans-serif'] = ['SimHei']
            plt.rcParams['axes.unicode_minus'] = False
        except Exception as e:
            print(f"设置中文字体失败: {e}")
        
        # 表格比较
        print("\n目标值比较 (越小越好):")
        headers = ["算法", "归一化距离", "最大风险1", "平均风险1", "最大风险2", "平均风险2"]
        print(f"{headers[0]:<15} {headers[1]:<15} {headers[2]:<15} {headers[3]:<15} {headers[4]:<15} {headers[5]:<15}")
        print("-" * 90)
        
        if 'dijkstra' in results:
            obj = results['dijkstra']['objectives']
            print(f"Dijkstra{'':<8} {obj[0]:<15.4f} {obj[1]:<15.4f} {obj[2]:<15.4f} {obj[3]:<15.4f} {obj[4]:<15.4f}")
            
        if 'astar' in results:
            obj = results['astar']['objectives']
            print(f"A*{'':<13} {obj[0]:<15.4f} {obj[1]:<15.4f} {obj[2]:<15.4f} {obj[3]:<15.4f} {obj[4]:<15.4f}")
            
        if 'theta' in results:
            obj = results['theta']['objectives']
            print(f"Theta*{'':<9} {obj[0]:<15.4f} {obj[1]:<15.4f} {obj[2]:<15.4f} {obj[3]:<15.4f} {obj[4]:<15.4f}")
        
        if 'moa' in results:
            moa = results['moa']
            print("-" * 90)
            print("MOA Theta* (最佳路径):")
            
            obj = moa['best_distance']['objectives']
            print(f"最短距离{'':<7} {obj[0]:<15.4f} {obj[1]:<15.4f} {obj[2]:<15.4f} {obj[3]:<15.4f} {obj[4]:<15.4f}")
            
            obj = moa['best_max_risk1']['objectives']
            print(f"最小最大风险1 {obj[0]:<15.4f} {obj[1]:<15.4f} {obj[2]:<15.4f} {obj[3]:<15.4f} {obj[4]:<15.4f}")
            
            obj = moa['best_avg_risk1']['objectives']
            print(f"最小平均风险1 {obj[0]:<15.4f} {obj[1]:<15.4f} {obj[2]:<15.4f} {obj[3]:<15.4f} {obj[4]:<15.4f}")
            
            obj = moa['best_max_risk2']['objectives']
            print(f"最小最大风险2 {obj[0]:<15.4f} {obj[1]:<15.4f} {obj[2]:<15.4f} {obj[3]:<15.4f} {obj[4]:<15.4f}")
            
            obj = moa['best_avg_risk2']['objectives']
            print(f"最小平均风险2 {obj[0]:<15.4f} {obj[1]:<15.4f} {obj[2]:<15.4f} {obj[3]:<15.4f} {obj[4]:<15.4f}")
            
        # 可视化路径比较
        plt.figure(figsize=(12, 10))
        
        # 绘制障碍物地图
        plt.imshow(self.ice_image, cmap='binary', origin='upper')
        
        # 绘制起点和终点
        plt.scatter(start[1], start[0], c='green', s=100, marker='*', label='起点')
        plt.scatter(goal[1], goal[0], c='red', s=100, marker='*', label='终点')
        
        # 绘制基线算法路径
        if 'dijkstra' in results:
            path = results['dijkstra']['path']
            xs = [p[1] for p in path]
            ys = [p[0] for p in path]
            plt.plot(xs, ys, 'b-', linewidth=2, marker='.', markersize=5, label='Dijkstra')
            
        if 'astar' in results:
            path = results['astar']['path']
            xs = [p[1] for p in path]
            ys = [p[0] for p in path]
            plt.plot(xs, ys, 'g-', linewidth=2, marker='.', markersize=5, label='A*')
            
        if 'theta' in results:
            path = results['theta']['path']
            xs = [p[1] for p in path]
            ys = [p[0] for p in path]
            plt.plot(xs, ys, 'c-', linewidth=2, marker='.', markersize=5, label='Theta*')
        
        # 绘制MOA最佳距离路径
        if 'moa' in results and 'best_distance' in results['moa']:
            path = results['moa']['best_distance']['path']
            xs = [p[1] for p in path]
            ys = [p[0] for p in path]
            plt.plot(xs, ys, 'r-', linewidth=3, marker='.', markersize=5, label='MOA-最短距离')
        
        plt.title('路径规划算法比较')
        plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=3)
        plt.tight_layout()
        plt.show()
        
        # 如果有多目标结果，绘制更多对比图
        if 'moa' in results:
            # 绘制不同目标最优路径
            self.visualize_moa_best_paths(results['moa'], start, goal)
            
            # 绘制Pareto前沿目标空间
            self.visualize_objective_space(results)
    
    def visualize_moa_best_paths(self, moa_results, start, goal):
        """可视化多目标优化的最佳路径"""
        plt.figure(figsize=(12, 10))
        
        # 绘制障碍物地图
        plt.imshow(self.ice_image, cmap='binary', origin='upper')
        
        # 绘制起点和终点
        plt.scatter(start[1], start[0], c='green', s=100, marker='*', label='起点')
        plt.scatter(goal[1], goal[0], c='red', s=100, marker='*', label='终点')
        
        # 绘制MOA不同最优目标的路径
        if 'best_distance' in moa_results:
            path = moa_results['best_distance']['path']
            xs = [p[1] for p in path]
            ys = [p[0] for p in path]
            plt.plot(xs, ys, 'r-', linewidth=2, marker='.', markersize=5, label='最短距离')
            
        if 'best_max_risk1' in moa_results:
            path = moa_results['best_max_risk1']['path']
            xs = [p[1] for p in path]
            ys = [p[0] for p in path]
            plt.plot(xs, ys, 'g-', linewidth=2, marker='.', markersize=5, label='最小最大风险1')
            
        if 'best_avg_risk1' in moa_results:
            path = moa_results['best_avg_risk1']['path']
            xs = [p[1] for p in path]
            ys = [p[0] for p in path]
            plt.plot(xs, ys, 'b-', linewidth=2, marker='.', markersize=5, label='最小平均风险1')
            
        if 'best_max_risk2' in moa_results:
            path = moa_results['best_max_risk2']['path']
            xs = [p[1] for p in path]
            ys = [p[0] for p in path]
            plt.plot(xs, ys, 'c-', linewidth=2, marker='.', markersize=5, label='最小最大风险2')
            
        if 'best_avg_risk2' in moa_results:
            path = moa_results['best_avg_risk2']['path']
            xs = [p[1] for p in path]
            ys = [p[0] for p in path]
            plt.plot(xs, ys, 'm-', linewidth=2, marker='.', markersize=5, label='最小平均风险2')
        
        plt.title('MOA Theta* 不同目标最优路径比较')
        plt.legend(loc='upper center', bbox_to_anchor=(0.5, -0.05), ncol=3)
        plt.tight_layout()
        plt.show()
    
    def visualize_objective_space(self, results):
        """可视化目标空间中的Pareto前沿与基线算法"""
        if 'moa' not in results or 'pareto_front' not in results['moa']:
            return
            
        # 提取Pareto前沿的所有目标值
        moa_objectives = np.array([p[1] for p in results['moa']['pareto_front']])
        
        # 创建目标空间可视化
        fig = plt.figure(figsize=(15, 10))
        
        # 2D投影: 距离 vs 最大风险1
        ax1 = fig.add_subplot(231)
        ax1.scatter(moa_objectives[:, 0], moa_objectives[:, 1], c='b', marker='o', label='Pareto前沿')
        
        if 'dijkstra' in results:
            ax1.scatter(results['dijkstra']['objectives'][0], results['dijkstra']['objectives'][1], 
                        c='r', marker='x', s=100, label='Dijkstra')
        if 'astar' in results:
            ax1.scatter(results['astar']['objectives'][0], results['astar']['objectives'][1], 
                        c='g', marker='s', s=100, label='A*')
        if 'theta' in results:
            ax1.scatter(results['theta']['objectives'][0], results['theta']['objectives'][1], 
                        c='m', marker='^', s=100, label='Theta*')
            
        ax1.set_xlabel('归一化距离')
        ax1.set_ylabel('最大风险1')
        ax1.set_title('距离 vs 最大风险1')
        ax1.legend()
        
        # 2D投影: 距离 vs 平均风险1
        ax2 = fig.add_subplot(232)
        ax2.scatter(moa_objectives[:, 0], moa_objectives[:, 2], c='b', marker='o', label='Pareto前沿')
        
        if 'dijkstra' in results:
            ax2.scatter(results['dijkstra']['objectives'][0], results['dijkstra']['objectives'][2], 
                        c='r', marker='x', s=100, label='Dijkstra')
        if 'astar' in results:
            ax2.scatter(results['astar']['objectives'][0], results['astar']['objectives'][2], 
                        c='g', marker='s', s=100, label='A*')
        if 'theta' in results:
            ax2.scatter(results['theta']['objectives'][0], results['theta']['objectives'][2], 
                        c='m', marker='^', s=100, label='Theta*')
            
        ax2.set_xlabel('归一化距离')
        ax2.set_ylabel('平均风险1')
        ax2.set_title('距离 vs 平均风险1')
        ax2.legend()
        
        # 2D投影: 距离 vs 最大风险2
        ax3 = fig.add_subplot(233)
        ax3.scatter(moa_objectives[:, 0], moa_objectives[:, 3], c='b', marker='o', label='Pareto前沿')
        
        if 'dijkstra' in results:
            ax3.scatter(results['dijkstra']['objectives'][0], results['dijkstra']['objectives'][3], 
                        c='r', marker='x', s=100, label='Dijkstra')
        if 'astar' in results:
            ax3.scatter(results['astar']['objectives'][0], results['astar']['objectives'][3], 
                        c='g', marker='s', s=100, label='A*')
        if 'theta' in results:
            ax3.scatter(results['theta']['objectives'][0], results['theta']['objectives'][3], 
                        c='m', marker='^', s=100, label='Theta*')
            
        ax3.set_xlabel('归一化距离')
        ax3.set_ylabel('最大风险2')
        ax3.set_title('距离 vs 最大风险2')
        ax3.legend()
        
        # 2D投影: 最大风险1 vs 最大风险2
        ax4 = fig.add_subplot(234)
        ax4.scatter(moa_objectives[:, 1], moa_objectives[:, 3], c='b', marker='o', label='Pareto前沿')
        
        if 'dijkstra' in results:
            ax4.scatter(results['dijkstra']['objectives'][1], results['dijkstra']['objectives'][3], 
                        c='r', marker='x', s=100, label='Dijkstra')
        if 'astar' in results:
            ax4.scatter(results['astar']['objectives'][1], results['astar']['objectives'][3], 
                        c='g', marker='s', s=100, label='A*')
        if 'theta' in results:
            ax4.scatter(results['theta']['objectives'][1], results['theta']['objectives'][3], 
                        c='m', marker='^', s=100, label='Theta*')
            
        ax4.set_xlabel('最大风险1')
        ax4.set_ylabel('最大风险2')
        ax4.set_title('最大风险1 vs 最大风险2')
        ax4.legend()
        
        # 2D投影: 平均风险1 vs 平均风险2
        ax5 = fig.add_subplot(235)
        ax5.scatter(moa_objectives[:, 2], moa_objectives[:, 4], c='b', marker='o', label='Pareto前沿')
        
        if 'dijkstra' in results:
            ax5.scatter(results['dijkstra']['objectives'][2], results['dijkstra']['objectives'][4], 
                        c='r', marker='x', s=100, label='Dijkstra')
        if 'astar' in results:
            ax5.scatter(results['astar']['objectives'][2], results['astar']['objectives'][4], 
                        c='g', marker='s', s=100, label='A*')
        if 'theta' in results:
            ax5.scatter(results['theta']['objectives'][2], results['theta']['objectives'][4], 
                        c='m', marker='^', s=100, label='Theta*')
            
        ax5.set_xlabel('平均风险1')
        ax5.set_ylabel('平均风险2')
        ax5.set_title('平均风险1 vs 平均风险2')
        ax5.legend()
        
        plt.tight_layout()
        plt.suptitle('目标空间比较：MOA Theta* vs 传统算法', fontsize=16)
        plt.subplots_adjust(top=0.9)
        plt.show()
        
    def evaluate_dominance(self, results):
        """评估MOA Theta*解集是否支配基线算法
        
        Returns:
            字典，包含每个基线算法是否被MOA支配
        """
        if 'moa' not in results or 'pareto_front' not in results['moa']:
            return {}
            
        dominance_results = {}
        moa_objs = np.array([p[1] for p in results['moa']['pareto_front']])
        
        for alg in ['dijkstra', 'astar', 'theta']: # 确保包含dijkstra
            if alg in results:
                baseline_obj = results[alg]['objectives']
                
                # 检查是否存在支配基线的解
                is_dominated = any(np.all(moa_obj <= baseline_obj) and np.any(moa_obj < baseline_obj) 
                                  for moa_obj in moa_objs)
                
                # 检查是否存在相等的解
                is_equal = any(np.all(moa_obj == baseline_obj) for moa_obj in moa_objs)
                
                dominance_results[alg] = {
                    'dominated_by_moa': is_dominated,
                    'equal_to_moa': is_equal
                }
                
                if is_dominated:
                    print(f"{alg.capitalize()} 算法解被MOA Theta*支配")
                elif is_equal:
                    print(f"{alg.capitalize()} 算法解在MOA Theta*的Pareto前沿上")
                else:
                    print(f"{alg.capitalize()} 算法解未被MOA Theta*支配")
        
        return dominance_results

    def evaluate_pareto_metrics(self, results):
        """使用标准帕累托效率指标评估多目标优化性能

        Args:
            results: 算法结果字典

        Returns:
            包含各种帕累托指标的字典
        """
        if 'moa' not in results or 'pareto_front' not in results['moa']:
            print("警告: MOA结果不完整，无法计算帕累托指标")
            return {}

        # 提取MOA的帕累托前沿
        moa_pareto_front = np.array([p[1] for p in results['moa']['pareto_front']])

        # 收集所有基线算法的目标值作为参考集
        reference_points = []
        for alg in ['dijkstra', 'astar', 'theta']:
            if alg in results and 'objectives' in results[alg]:
                reference_points.append(results[alg]['objectives'])

        if not reference_points:
            print("警告: 没有基线算法结果用于比较")
            return {}

        reference_set = np.array(reference_points)

        # 计算标准帕累托指标
        metrics = {}

        # 1. 超容量指标 (Hypervolume)
        metrics['hypervolume'] = self._calculate_hypervolume(moa_pareto_front, reference_set)

        # 2. 传播指标 (Spread/Spacing)
        metrics['spread'] = self._calculate_spread(moa_pareto_front)
        metrics['spacing'] = self._calculate_spacing(moa_pareto_front)

        # 3. 收敛性指标
        metrics['convergence'] = self._calculate_convergence(moa_pareto_front, reference_set)

        # 4. 覆盖率指标
        metrics['coverage'] = self._calculate_coverage(moa_pareto_front, reference_set)

        # 5. 帕累托前沿大小
        metrics['pareto_front_size'] = len(moa_pareto_front)

        # 6. 多样性指标
        metrics['diversity'] = self._calculate_diversity(moa_pareto_front)

        return metrics

    def _calculate_hypervolume(self, pareto_front, reference_set):
        """计算超容量指标 (Hypervolume Indicator)

        超容量是多目标优化中最重要的指标之一，测量帕累托前沿覆盖的目标空间体积
        """
        if len(pareto_front) == 0:
            return 0.0

        # 确定参考点 (通常是所有目标的最大值)
        all_points = np.vstack([pareto_front, reference_set])
        ref_point = np.max(all_points, axis=0) + 0.1  # 稍微增加以确保所有点都被包含

        # 使用简化的超容量计算 (对于2D情况)
        if pareto_front.shape[1] == 2:
            return self._hypervolume_2d(pareto_front, ref_point)
        else:
            # 对于高维情况，使用蒙特卡洛方法近似
            return self._hypervolume_monte_carlo(pareto_front, ref_point)

    def _hypervolume_2d(self, pareto_front, ref_point):
        """2D情况下的精确超容量计算"""
        # 按第一个目标排序
        sorted_front = pareto_front[np.argsort(pareto_front[:, 0])]

        volume = 0.0
        prev_x = 0.0

        for point in sorted_front:
            if point[0] < ref_point[0] and point[1] < ref_point[1]:
                width = point[0] - prev_x
                height = ref_point[1] - point[1]
                volume += width * height
                prev_x = point[0]

        return volume

    def _hypervolume_monte_carlo(self, pareto_front, ref_point, n_samples=10000):
        """使用蒙特卡洛方法近似计算超容量"""
        # 生成随机点
        min_bounds = np.min(pareto_front, axis=0)
        random_points = np.random.uniform(min_bounds, ref_point, (n_samples, len(ref_point)))

        # 计算被帕累托前沿支配的点的比例
        dominated_count = 0
        for point in random_points:
            if any(np.all(pf_point <= point) for pf_point in pareto_front):
                dominated_count += 1

        # 计算总体积
        total_volume = np.prod(ref_point - min_bounds)
        return (dominated_count / n_samples) * total_volume

    def _calculate_spread(self, pareto_front):
        """计算传播指标 (Spread Indicator)

        测量帕累托前沿解的分布范围
        """
        if len(pareto_front) <= 1:
            return 0.0

        # 计算每个目标维度的范围
        ranges = np.max(pareto_front, axis=0) - np.min(pareto_front, axis=0)

        # 返回范围的平均值（归一化）
        return np.mean(ranges)

    def _calculate_spacing(self, pareto_front):
        """计算间距指标 (Spacing Indicator)

        测量帕累托前沿解之间距离分布的均匀性
        """
        if len(pareto_front) <= 1:
            return 0.0

        # 计算每个点到其最近邻的距离
        distances = []
        for i, point in enumerate(pareto_front):
            min_dist = float('inf')
            for j, other_point in enumerate(pareto_front):
                if i != j:
                    dist = np.linalg.norm(point - other_point)
                    min_dist = min(min_dist, dist)
            distances.append(min_dist)

        # 计算距离的标准差（越小越好）
        mean_dist = np.mean(distances)
        spacing = np.sqrt(np.mean([(d - mean_dist)**2 for d in distances]))

        return spacing

    def _calculate_convergence(self, pareto_front, reference_set):
        """计算收敛性指标 (Convergence Indicator)

        测量帕累托前沿与真实帕累托前沿的接近程度
        使用参考集作为真实前沿的近似
        """
        if len(pareto_front) == 0 or len(reference_set) == 0:
            return float('inf')

        # 计算帕累托前沿中每个点到参考集的最小距离
        min_distances = []
        for pf_point in pareto_front:
            min_dist = min(np.linalg.norm(pf_point - ref_point) for ref_point in reference_set)
            min_distances.append(min_dist)

        # 返回平均最小距离
        return np.mean(min_distances)

    def _calculate_coverage(self, pareto_front, reference_set):
        """计算覆盖率指标 (Coverage Indicator)

        测量帕累托前沿相对于参考集的覆盖程度
        """
        if len(pareto_front) == 0 or len(reference_set) == 0:
            return 0.0

        # 计算被帕累托前沿支配或等于的参考点数量
        dominated_count = 0
        for ref_point in reference_set:
            for pf_point in pareto_front:
                # 检查是否支配或相等
                if (np.all(pf_point <= ref_point) and np.any(pf_point < ref_point)) or np.allclose(pf_point, ref_point):
                    dominated_count += 1
                    break

        return dominated_count / len(reference_set)

    def _calculate_diversity(self, pareto_front):
        """计算多样性指标 (Diversity Indicator)

        测量帕累托前沿解的多样性
        """
        if len(pareto_front) <= 1:
            return 0.0

        # 计算所有点对之间的平均距离
        total_distance = 0.0
        count = 0

        for i in range(len(pareto_front)):
            for j in range(i + 1, len(pareto_front)):
                total_distance += np.linalg.norm(pareto_front[i] - pareto_front[j])
                count += 1

        return total_distance / count if count > 0 else 0.0

    def print_pareto_metrics(self, metrics):
        """打印帕累托效率指标的详细报告"""
        if not metrics:
            print("❌ 无法计算帕累托效率指标")
            return

        print("\n" + "="*70)
        print("📊 MOA-Theta* 帕累托效率指标评估报告")
        print("="*70)

        print(f"🎯 超容量指标 (Hypervolume):     {metrics.get('hypervolume', 'N/A'):.6f}")
        print(f"   - 衡量帕累托前沿覆盖的目标空间体积")
        print(f"   - 值越大表示性能越好")

        print(f"\n📏 传播指标 (Spread):           {metrics.get('spread', 'N/A'):.6f}")
        print(f"   - 衡量解在目标空间中的分布范围")
        print(f"   - 值越大表示覆盖范围越广")

        print(f"\n📐 间距指标 (Spacing):          {metrics.get('spacing', 'N/A'):.6f}")
        print(f"   - 衡量解之间距离分布的均匀性")
        print(f"   - 值越小表示分布越均匀")

        print(f"\n🎯 收敛性指标 (Convergence):    {metrics.get('convergence', 'N/A'):.6f}")
        print(f"   - 衡量与理想帕累托前沿的接近程度")
        print(f"   - 值越小表示收敛性越好")

        print(f"\n📊 覆盖率指标 (Coverage):       {metrics.get('coverage', 'N/A'):.6f}")
        print(f"   - 衡量相对于参考算法的覆盖程度")
        print(f"   - 值越大表示覆盖越全面")

        print(f"\n🔢 帕累托前沿大小:              {metrics.get('pareto_front_size', 'N/A')}")
        print(f"   - 帕累托前沿中非支配解的数量")
        print(f"   - 更多解提供更多选择")

        print(f"\n🌈 多样性指标 (Diversity):      {metrics.get('diversity', 'N/A'):.6f}")
        print(f"   - 衡量解集的多样性")
        print(f"   - 值越大表示解越多样化")

        print("="*70)

        # 综合评估
        self._provide_comprehensive_assessment(metrics)

    def _provide_comprehensive_assessment(self, metrics):
        """提供综合性能评估"""
        print("\n🔍 综合性能评估:")

        # 评估各个指标
        assessments = []

        if 'hypervolume' in metrics and metrics['hypervolume'] > 0:
            assessments.append("✅ 超容量指标显示算法能够有效覆盖目标空间")

        if 'coverage' in metrics:
            if metrics['coverage'] > 0.8:
                assessments.append("✅ 高覆盖率表明算法优于大部分基线方法")
            elif metrics['coverage'] > 0.5:
                assessments.append("⚠️  中等覆盖率，算法在某些方面优于基线方法")
            else:
                assessments.append("❌ 低覆盖率，算法可能需要进一步优化")

        if 'spacing' in metrics:
            if metrics['spacing'] < 0.1:
                assessments.append("✅ 良好的解分布均匀性")
            else:
                assessments.append("⚠️  解分布可能不够均匀，建议增加多样性")

        if 'pareto_front_size' in metrics:
            if metrics['pareto_front_size'] >= 5:
                assessments.append("✅ 帕累托前沿提供了丰富的解选择")
            else:
                assessments.append("⚠️  帕累托前沿解数量较少，可能需要更多探索")

        for assessment in assessments:
            print(f"   {assessment}")

        if not assessments:
            print("   ❌ 指标数据不足，无法进行综合评估")

# --- Optuna 优化部分 (目标函数需要修改以适应新plan_path) ---

def objective(trial, ice_image, risk_map1, risk_map2, start, goal):
    """Optuna 优化目标函数 - 评价最终Pareto前沿质量并比较基线算法"""
    # 从 trial 获取所有需要优化的参数
    params = {
        'angle_constraint': trial.suggest_categorical('angle_constraint', [None, math.pi / 6, math.pi / 4, math.pi / 3]),
        'max_iterations': trial.suggest_int('max_iterations', 15000, 30000, step=5000),
        'sampling_radius': trial.suggest_float('sampling_radius', 1.5, 6.0, step=0.5), # <-- 添加 sampling_radius
        'num_samples': trial.suggest_int('num_samples', 16, 24, step=4),
        'nsga3_pop_size': trial.suggest_int('nsga3_pop_size', 40, 120, step=10),     # <-- 添加 nsga3_pop_size
        # 注意：nsga3_ref_points 通常不作为优化参数，保持固定或基于维度计算
        'max_generations': trial.suggest_int('max_generations', 30, 70, step=10),
        'crossover_rate': trial.suggest_float('crossover_rate', 0.7, 0.95, step=0.05),
        'mutation_rate': trial.suggest_float('mutation_rate', 0.1, 0.25, step=0.05),
        'num_initial_paths_diverse': trial.suggest_int('num_initial_paths_diverse', 8, 16, step=2),
        'num_initial_paths_targeted': trial.suggest_int('num_initial_paths_targeted', 4, 10, step=2),
        'smoothing_factor': trial.suggest_float('smoothing_factor', 0.2, 0.8, step=0.1),
        'smoothing_iterations': trial.suggest_int('smoothing_iterations', 1, 5),
        'max_macro_iterations': trial.suggest_int('max_macro_iterations', 2, 5) # 优化宏观迭代次数
    }
    
    # 创建 planner 实例时传入 nsga3_pop_size
    planner = HierarchicalMOAThetaStar(ice_image, risk_map1, risk_map2, 
                                         nsga3_pop_size=params['nsga3_pop_size'])
    try:
        print(f"\n运行 Trial {trial.number} 使用参数: {params}...")
        
        # 运行多目标优化 (plan_path 内部会计算基线)
        final_results_dict = planner.plan_path(start, goal, params)
        
        # --- 数据有效性检查和提取 ---
        moa_results = final_results_dict.get('moa', {})
        pareto_front_tuples_raw = moa_results.get('pareto_front', []) # List of (path, objectives)

        # 过滤无效的 MOA 结果
        valid_pareto_front_tuples = []
        for path, objectives in pareto_front_tuples_raw:
             if isinstance(objectives, (list, np.ndarray)) and len(objectives) == planner.nsga3.num_objectives and all(isinstance(x, (int, float)) and math.isfinite(x) for x in objectives):
                 valid_pareto_front_tuples.append((path, objectives))
             else:
                 print(f"警告 Trial {trial.number}: 发现无效的 MOA 目标值: {objectives}，已跳过")

        num_paths = len(valid_pareto_front_tuples)

        if num_paths == 0:
            print(f"Trial {trial.number}: 未找到有效的Pareto前沿，得分为0")
            # 返回一个很差的分数，因为我们要最大化
            return 0.0

        # 提取有效的 MOA 目标值
        moa_objectives = np.array([p[1] for p in valid_pareto_front_tuples])

        # 提取并过滤有效的基线目标值
        baseline_objectives_dict = {}
        for alg in ['dijkstra', 'astar', 'theta']:
            if alg in final_results_dict and 'objectives' in final_results_dict[alg]:
                 obj_list = final_results_dict[alg]['objectives']
                 if isinstance(obj_list, (list, np.ndarray)) and len(obj_list) == planner.nsga3.num_objectives and all(isinstance(x, (int, float)) and math.isfinite(x) for x in obj_list):
                    baseline_objectives_dict[alg] = np.array(obj_list) # 存储为NumPy数组
                 else:
                     print(f"警告 Trial {trial.number}: 基线算法 {alg} 的目标值无效或格式错误: {obj_list}")
        # --- 数据有效性检查结束 ---

        # --- 使用标准帕累托效率指标评估 ---
        print(f"Trial {trial.number}: 开始计算标准帕累托效率指标...")

        # 准备结果字典用于指标计算
        results_for_metrics = {
            'moa': {
                'pareto_front': valid_pareto_front_tuples
            }
        }

        # 添加基线算法结果
        for alg, obj_array in baseline_objectives_dict.items():
            results_for_metrics[alg] = {
                'objectives': obj_array
            }

        # 计算标准帕累托指标
        pareto_metrics = planner.evaluate_pareto_metrics(results_for_metrics)

        if pareto_metrics:
            print(f"Trial {trial.number}: 帕累托指标计算完成")
            print(f"  - 超容量: {pareto_metrics.get('hypervolume', 0):.6f}")
            print(f"  - 传播: {pareto_metrics.get('spread', 0):.6f}")
            print(f"  - 间距: {pareto_metrics.get('spacing', 0):.6f}")
            print(f"  - 收敛性: {pareto_metrics.get('convergence', 0):.6f}")
            print(f"  - 覆盖率: {pareto_metrics.get('coverage', 0):.6f}")
            print(f"  - 多样性: {pareto_metrics.get('diversity', 0):.6f}")

            # 使用超容量作为主要指标
            hypervolume_value = pareto_metrics.get('hypervolume', 0.0)
        else:
            print(f"Trial {trial.number}: 帕累托指标计算失败，使用默认值")
            hypervolume_value = 0.0
            pareto_metrics = {}

        # --- 计算支配分数和超越基线奖励 ---
        dominance_score = 0
        # 使用 baseline_objectives_dict (已过滤) 进行比较
        for alg, baseline_obj in baseline_objectives_dict.items():
            # 检查是否至少有一个MOA解支配基线算法
            try:
                # 确保 baseline_obj 是 NumPy 数组
                if not isinstance(baseline_obj, np.ndarray):
                     baseline_obj = np.array(baseline_obj)
                is_dominating = any(np.all(moa_obj <= baseline_obj) and np.any(moa_obj < baseline_obj)
                                   for moa_obj in moa_objectives) # 使用过滤后的 moa_objectives
                if is_dominating:
                    dominance_score += 1 # 每个支配的基线算法+1分
                    print(f"Trial {trial.number}: 支配了 {alg} 算法")
            except TypeError as te:
                 print(f"比较 {alg} 时发生类型错误: {te}. MOA obj type: {type(moa_objectives[0]) if len(moa_objectives)>0 else 'N/A'}, Baseline obj type: {type(baseline_obj)}")
            except Exception as ex:
                 print(f"比较 {alg} 时发生未知错误: {ex}")

        # 新增：检查MOA最短距离路径是否优于或等于Theta*基线
        beat_theta_star_bonus = 0
        # 使用 baseline_objectives_dict 和 valid_pareto_front_tuples
        if 'theta' in baseline_objectives_dict and valid_pareto_front_tuples:
            try:
                # 找到MOA的最短距离解的目标值
                best_dist_idx = min(range(num_paths), key=lambda i: moa_objectives[i, 0])
                moa_best_dist_obj = moa_objectives[best_dist_idx]

                # 获取Theta*的目标值
                theta_obj = baseline_objectives_dict['theta'] # 从过滤后的字典获取

                # 检查MOA最短距离解是否在所有维度上都不差于Theta*
                if np.all(moa_best_dist_obj <= theta_obj):
                    # 显著增加奖励，使其成为主要优化目标之一
                    beat_theta_star_bonus = 0.1 * hypervolume_value # 奖励设为超体积的10%
                    # beat_theta_star_bonus = 20.0 # 或者保持固定值奖励
                    print(f"Trial {trial.number}: MOA最短距离解全面优于或等于Theta*基线！获得额外奖励 {beat_theta_star_bonus:.4f}")
            except Exception as e:
                print(f"检查超越Theta*时出错: {e}")

        # --- 不再需要单独计算多样性，HV已包含此信息 ---
        # try:
            # ... (多样性计算代码删除)
        # except Exception as e:
        #     print(f"计算多样性时出错: {e}")
        #     diversity = 0.0

        # --- 基于多个帕累托指标的综合评分 ---
        # 使用加权组合多个帕累托指标

        # 基础分数：超容量指标（主要指标）
        hypervolume_score = hypervolume_value

        # 覆盖率奖励：鼓励算法优于基线方法
        coverage_bonus = pareto_metrics.get('coverage', 0.0) * 0.5

        # 多样性奖励：鼓励解的多样性
        diversity_bonus = pareto_metrics.get('diversity', 0.0) * 0.1

        # 传播奖励：鼓励更广的解分布
        spread_bonus = pareto_metrics.get('spread', 0.0) * 0.1

        # 间距惩罚：惩罚分布不均匀的解集（间距越小越好）
        spacing_penalty = -pareto_metrics.get('spacing', 1.0) * 0.05

        # 收敛性奖励：奖励接近理想前沿的解（收敛性越小越好）
        convergence_bonus = -pareto_metrics.get('convergence', 1.0) * 0.05

        # 帕累托前沿大小奖励：鼓励更多的非支配解
        size_bonus = min(pareto_metrics.get('pareto_front_size', 0), 10) * 0.02

        # 支配分数因子
        dominance_factor = 1.0 + dominance_score * 0.1

        # 综合评分
        pareto_quality_score = (
            hypervolume_score * dominance_factor +
            coverage_bonus +
            diversity_bonus +
            spread_bonus +
            spacing_penalty +
            convergence_bonus +
            size_bonus +
            beat_theta_star_bonus
        )

        print(f"Trial {trial.number}: 帕累托指标详细评分:")
        print(f"  - 超容量: {hypervolume_score:.4f}")
        print(f"  - 覆盖率奖励: {coverage_bonus:.4f}")
        print(f"  - 多样性奖励: {diversity_bonus:.4f}")
        print(f"  - 传播奖励: {spread_bonus:.4f}")
        print(f"  - 间距惩罚: {spacing_penalty:.4f}")
        print(f"  - 收敛性奖励: {convergence_bonus:.4f}")
        print(f"  - 前沿大小奖励: {size_bonus:.4f}")
        print(f"  - 支配因子: {dominance_factor:.2f}")
        print(f"  - 超越Theta*奖励: {beat_theta_star_bonus:.4f}")
        print(f"Trial {trial.number}: 最终帕累托综合得分 = {pareto_quality_score:.4f}")

        return pareto_quality_score

    except Exception as e:
        print(f"Trial {trial.number} 失败: {e}")
        return 0.0

# ... (run_optimization 不变, 但 direction 可能需要调整为 'maximize' 如果返回 quality_score) ...
def run_optimization(ice_image, risk_map1, risk_map2, start, goal, n_trials=50):
    """使用 Optuna 进行参数优化，基于标准帕累托效率指标最大化MOA Theta*性能"""
    # 使用 maximize 方向，因为现在我们的目标函数返回的是基于帕累托指标的综合质量分数
    study = optuna.create_study(direction='maximize')
    func = lambda trial: objective(trial, ice_image, risk_map1, risk_map2, start, goal)

    print("\n开始Optuna优化，寻找最佳参数组合...")
    print("优化目标：基于标准帕累托效率指标最大化多目标优化性能")
    print("评估指标：超容量、传播、间距、收敛性、覆盖率、多样性")
    print(f"计划运行 {n_trials} 次试验")

    # 使用 n_jobs=-1 来并行化试验，利用所有可用CPU核心
    # 注意：这要求 objective 函数和其调用的所有代码是进程安全的
    # 在 Windows 上，请确保所有顶级可执行代码都在 if __name__ == "__main__": 块内
    try:
        study.optimize(func, n_trials=n_trials, n_jobs=-1) # <--- 修改这里
    except Exception as e:
         print(f"Optuna 并行优化过程中发生错误: {e}")
         print("尝试以非并行模式 (n_jobs=1) 重新运行...")
         # 如果并行出错，尝试单核运行
         study.optimize(func, n_trials=n_trials, n_jobs=1)


    print("\n优化完成！")
    print(f"最佳试验编号: {study.best_trial.number}")
    print(f"最佳得分: {study.best_value:.4f}")
    print("最佳参数组合:")
    for key, value in study.best_params.items():
        print(f"  {key}: {value}")

    # 输出几个最佳试验的统计信息
    print("\n前3名试验统计:")
    sorted_trials = sorted(study.trials, key=lambda t: t.value if t.value is not None else float('-inf'), reverse=True)
    for i, trial in enumerate(sorted_trials[:3]):
        if trial.value is not None:
            print(f"第{i+1}名 - 试验 {trial.number}: 得分 = {trial.value:.4f}")
            print(f"  参数: {trial.params}")

    return study.best_params

# --- 修改后的测试函数 --- 
def test_hierarchical_moa_thetastar(optimize_params=True, n_trials=20):
    """测试分层架构的多目标Theta*算法，可选参数优化"""
    # 创建测试地图
    np.random.seed(42)
    size = 100
    ice_image = np.zeros((size, size), dtype=int)
    
    # 添加随机障碍物 (减少一些，避免地图过于拥堵)
    for _ in range(250):
        x, y = np.random.randint(0, size, 2)
        # 创建小块障碍
        for dx in range(-1, 2):
            for dy in range(-1, 2):
                nx, ny = x + dx, y + dy
                if 0 <= nx < size and 0 <= ny < size:
                    ice_image[nx, ny] = 1
    
    # 创建风险区域
    risk_map1 = np.zeros_like(ice_image, dtype=float)  # 安全度地图（风险1）
    risk_map2 = np.zeros_like(ice_image, dtype=float)  # 环境风险地图（风险2）
    
    # 添加安全风险区域（风险1）
    risk_centers1 = [(20, 30), (50, 50), (70, 25), (30, 70)]
    for cx, cy in risk_centers1:
        radius = np.random.randint(10, 20)
        for x in range(max(0, cx-radius), min(size, cx+radius)):
            for y in range(max(0, cy-radius), min(size, cy+radius)):
                dist = math.sqrt((x-cx)**2 + (y-cy)**2)
                if dist <= radius:
                    risk_map1[x, y] = max(risk_map1[x, y], (1.0 - dist/radius) * 0.8)
    
    # 添加环境风险区域（风险2）
    risk_centers2 = [(35, 60), (75, 45), (40, 20)]
    for cx, cy in risk_centers2:
        radius = np.random.randint(10, 20)
        for x in range(max(0, cx-radius), min(size, cx+radius)):
            for y in range(max(0, cy-radius), min(size, cy+radius)):
                dist = math.sqrt((x-cx)**2 + (y-cy)**2)
                if dist <= radius:
                    risk_map2[x, y] = max(risk_map2[x, y], (1.0 - dist/radius) * 0.9)
    
    # 设置起点和终点
    start = (5, 5)
    goal = (95, 95)
    
    # 确保起点和终点可通行
    for r in range(3):
        for x in range(max(0, start[0]-r), min(size, start[0]+r+1)):
            for y in range(max(0, start[1]-r), min(size, start[1]+r+1)):
                ice_image[x, y] = 0
                risk_map1[x, y] = 0
                risk_map2[x, y] = 0
                
        for x in range(max(0, goal[0]-r), min(size, goal[0]+r+1)):
            for y in range(max(0, goal[1]-r), min(size, goal[1]+r+1)):
                ice_image[x, y] = 0
                risk_map1[x, y] = 0
                risk_map2[x, y] = 0
    
    best_params = None
    if optimize_params:
        print("--- 开始参数优化 --- ")
        # 注意：优化现在可能需要更长时间，因为每次试验都运行多次宏迭代
        best_params = run_optimization(ice_image, risk_map1, risk_map2, start, goal, n_trials=n_trials)
        print("--- 参数优化结束 --- ")
    
    final_params = best_params if best_params else None 
        
    planner = HierarchicalMOAThetaStar(ice_image, risk_map1, risk_map2)

    # 运行多目标优化和比较
    # optimize_first=True 确保 plan_path 被调用，它现在内部处理基线和优化
    results = planner.run_and_compare_baselines(start, goal, optimize_first=True, params=final_params)
    
    # 评估支配关系
    print("\n=== 评估支配关系 ===")
    dominance_results = planner.evaluate_dominance(results)

    # 计算标准帕累托效率指标
    print("\n=== 标准帕累托效率指标评估 ===")
    pareto_metrics = planner.evaluate_pareto_metrics(results)
    if pareto_metrics:
        planner.print_pareto_metrics(pareto_metrics)
    else:
        print("❌ 无法计算帕累托效率指标，可能是由于数据不完整")

    # 总结分析
    print("\n=== 总结分析 ===")
    if 'moa' in results and 'pareto_front' in results['moa']:
        paths_count = len(results['moa']['pareto_front'])
        print(f"MOA Theta* 共生成 {paths_count} 条Pareto最优路径")
        
        # 统计支配其他算法的情况
        dominated_count = sum(1 for alg in dominance_results if alg in dominance_results and dominance_results[alg]['dominated_by_moa'])
        
        # 检查MOA最短距离解是否优于Theta*
        better_than_theta = False
        if 'theta' in results and 'moa' in results and 'best_distance' in results['moa']:
             moa_dist_obj = results['moa']['best_distance']['objectives']
             theta_obj = results['theta']['objectives']
             if np.all(moa_dist_obj <= theta_obj):
                 better_than_theta = True
        
        num_baselines = len([k for k in results if k != 'moa']) # 计算实际运行的基线数量
        
        if dominated_count == num_baselines:
            print(f"MOA Theta* 方法成功支配所有 {num_baselines} 个基线算法！")
        elif dominated_count > 0:
            print(f"MOA Theta* 方法支配了 {dominated_count}/{num_baselines} 个基线算法")
        else:
            print(f"MOA Theta* 方法未能支配任何 {num_baselines} 个基线算法")
            
        if better_than_theta:
             print("并且，MOA找到的最短距离路径在所有目标上不差于经典Theta*路径。")
        elif 'theta' in results:
             print("但是，MOA找到的最短距离路径未能全面优于经典Theta*路径。")
            
        if dominated_count < num_baselines or not better_than_theta:
             print("建议进一步调整优化参数或目标函数以增强优势。")
    else:
        print("多目标优化未成功生成Pareto前沿")

if __name__ == "__main__":

    PERFORM_OPTIMIZATION = True # 设为 True 运行优化
    # 增加试验次数以更好地利用并行化，例如 50 次或更多
    N_OPTIMIZATION_TRIALS = 50

    #test_hierarchical_moa_thetastar(optimize_params=PERFORM_OPTIMIZATION, n_trials=N_OPTIMIZATION_TRIALS) 