"""
RRT和RRT*路径规划算法
基于python_motion_planning-master项目适配海冰分割系统
"""
import math
import random
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional

class Node:
    """RRT节点类"""
    def __init__(self, x: float, y: float, parent=None):
        self.x = x
        self.y = y
        self.parent = parent
        self.cost = 0.0  # 从起点到当前节点的代价
    
    def __eq__(self, other):
        if not isinstance(other, Node):
            return False
        return abs(self.x - other.x) < 1e-6 and abs(self.y - other.y) < 1e-6
    
    def __hash__(self):
        return hash((round(self.x, 6), round(self.y, 6)))

class RRTPathPlanner:
    """RRT路径规划器"""
    
    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int], grid_map: np.ndar<PERSON>,
                 max_iter: int = 1000, step_size: float = 5.0, goal_sample_rate: float = 0.1):
        """
        初始化RRT规划器
        
        Args:
            start: 起点坐标 (row, col)
            goal: 终点坐标 (row, col)
            grid_map: 网格地图 (0=可通行, 1=障碍物)
            max_iter: 最大迭代次数
            step_size: 步长
            goal_sample_rate: 目标采样率
        """
        # 转换坐标系：(row, col) -> (x, y)
        self.start = Node(start[1], start[0])  # (col, row) -> (x, y)
        self.goal = Node(goal[1], goal[0])
        
        self.grid_map = grid_map
        self.map_height, self.map_width = grid_map.shape
        
        self.max_iter = max_iter
        self.step_size = step_size
        self.goal_sample_rate = goal_sample_rate
        
        # 节点列表
        self.node_list = [self.start]
        
        # 统计信息
        self.path_found = False
        self.final_path = None
        self.iterations_used = 0
    
    def is_collision_free(self, node: Node) -> bool:
        """检查节点是否无碰撞"""
        x, y = int(round(node.x)), int(round(node.y))
        
        # 检查边界
        if x < 0 or x >= self.map_width or y < 0 or y >= self.map_height:
            return False
        
        # 检查障碍物
        return self.grid_map[y, x] == 0
    
    def is_path_collision_free(self, from_node: Node, to_node: Node) -> bool:
        """检查两点间路径是否无碰撞"""
        # 使用Bresenham算法检查路径上的所有点
        x0, y0 = int(round(from_node.x)), int(round(from_node.y))
        x1, y1 = int(round(to_node.x)), int(round(to_node.y))
        
        points = self.bresenham_line(x0, y0, x1, y1)
        
        for x, y in points:
            if x < 0 or x >= self.map_width or y < 0 or y >= self.map_height:
                return False
            if self.grid_map[y, x] == 1:
                return False
        
        return True
    
    def bresenham_line(self, x0: int, y0: int, x1: int, y1: int) -> List[Tuple[int, int]]:
        """Bresenham直线算法"""
        points = []
        dx = abs(x1 - x0)
        dy = abs(y1 - y0)
        sx = 1 if x0 < x1 else -1
        sy = 1 if y0 < y1 else -1
        err = dx - dy
        
        x, y = x0, y0
        
        while True:
            points.append((x, y))
            
            if x == x1 and y == y1:
                break
                
            e2 = 2 * err
            if e2 > -dy:
                err -= dy
                x += sx
            if e2 < dx:
                err += dx
                y += sy
        
        return points
    
    def sample_random_node(self) -> Node:
        """随机采样节点"""
        if random.random() < self.goal_sample_rate:
            # 以一定概率采样目标点
            return Node(self.goal.x, self.goal.y)
        else:
            # 随机采样
            x = random.uniform(0, self.map_width - 1)
            y = random.uniform(0, self.map_height - 1)
            return Node(x, y)
    
    def find_nearest_node(self, sample_node: Node) -> Node:
        """找到最近的节点"""
        min_dist = float('inf')
        nearest_node = None
        
        for node in self.node_list:
            dist = self.distance(node, sample_node)
            if dist < min_dist:
                min_dist = dist
                nearest_node = node
        
        return nearest_node
    
    def steer(self, from_node: Node, to_node: Node) -> Node:
        """从from_node向to_node方向扩展step_size距离"""
        dist = self.distance(from_node, to_node)
        
        if dist <= self.step_size:
            return Node(to_node.x, to_node.y, from_node)
        
        # 计算方向
        theta = math.atan2(to_node.y - from_node.y, to_node.x - from_node.x)
        
        # 扩展
        new_x = from_node.x + self.step_size * math.cos(theta)
        new_y = from_node.y + self.step_size * math.sin(theta)
        
        return Node(new_x, new_y, from_node)
    
    def distance(self, node1: Node, node2: Node) -> float:
        """计算两节点间距离"""
        return math.hypot(node2.x - node1.x, node2.y - node1.y)
    
    def is_goal_reached(self, node: Node) -> bool:
        """检查是否到达目标"""
        return self.distance(node, self.goal) <= self.step_size
    
    def extract_path(self, goal_node: Node) -> List[Tuple[int, int]]:
        """提取路径"""
        path = []
        current = goal_node
        
        while current is not None:
            # 转换回(row, col)格式
            path.append((int(round(current.y)), int(round(current.x))))
            current = current.parent
        
        path.reverse()
        return path
    
    def plan(self) -> Optional[List[Tuple[int, int]]]:
        """RRT路径规划"""
        print(f"开始RRT规划: 最大迭代{self.max_iter}次")
        
        for i in range(self.max_iter):
            self.iterations_used = i + 1
            
            # 1. 随机采样
            sample_node = self.sample_random_node()
            
            # 2. 找到最近节点
            nearest_node = self.find_nearest_node(sample_node)
            
            # 3. 扩展
            new_node = self.steer(nearest_node, sample_node)
            
            # 4. 碰撞检测
            if not self.is_collision_free(new_node):
                continue
            
            if not self.is_path_collision_free(nearest_node, new_node):
                continue
            
            # 5. 添加到树中
            new_node.parent = nearest_node
            new_node.cost = nearest_node.cost + self.distance(nearest_node, new_node)
            self.node_list.append(new_node)
            
            # 6. 检查是否到达目标
            if self.is_goal_reached(new_node):
                # 尝试直接连接到目标
                goal_node = Node(self.goal.x, self.goal.y, new_node)
                if self.is_path_collision_free(new_node, goal_node):
                    goal_node.cost = new_node.cost + self.distance(new_node, goal_node)
                    self.node_list.append(goal_node)
                    
                    print(f"RRT找到路径! 迭代次数: {i+1}")
                    self.path_found = True
                    self.final_path = self.extract_path(goal_node)
                    return self.final_path
            
            # 打印进度
            if (i + 1) % 100 == 0:
                print(f"RRT迭代: {i+1}/{self.max_iter}")
        
        print("RRT未找到路径")
        return None
    
    def visualize(self, show_tree: bool = True):
        """可视化结果"""
        plt.figure(figsize=(10, 8))
        
        # 显示地图
        plt.imshow(self.grid_map, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 显示树
        if show_tree:
            for node in self.node_list[1:]:  # 跳过起点
                if node.parent:
                    plt.plot([node.parent.x, node.x], [node.parent.y, node.y], 
                            'b-', alpha=0.3, linewidth=0.5)
        
        # 显示路径
        if self.final_path:
            path_array = np.array(self.final_path)
            plt.plot(path_array[:, 1], path_array[:, 0], 'r-', linewidth=3, label='RRT Path')
        
        # 显示起点和终点
        plt.plot(self.start.x, self.start.y, 'go', markersize=10, label='Start')
        plt.plot(self.goal.x, self.goal.y, 'ro', markersize=10, label='Goal')
        
        plt.title(f'RRT路径规划 (迭代: {self.iterations_used})')
        plt.legend()
        plt.axis('equal')
        plt.grid(True, alpha=0.3)
        plt.show()

class RRTStarPathPlanner(RRTPathPlanner):
    """RRT*路径规划器"""
    
    def __init__(self, start: Tuple[int, int], goal: Tuple[int, int], grid_map: np.ndarray,
                 max_iter: int = 1000, step_size: float = 5.0, goal_sample_rate: float = 0.1,
                 search_radius: float = 10.0):
        """
        初始化RRT*规划器
        
        Args:
            search_radius: 重连搜索半径
        """
        super().__init__(start, goal, grid_map, max_iter, step_size, goal_sample_rate)
        self.search_radius = search_radius
    
    def find_near_nodes(self, new_node: Node) -> List[Node]:
        """找到新节点附近的所有节点"""
        near_nodes = []
        for node in self.node_list:
            if self.distance(node, new_node) <= self.search_radius:
                near_nodes.append(node)
        return near_nodes
    
    def choose_parent(self, new_node: Node, near_nodes: List[Node]) -> Node:
        """为新节点选择最优父节点"""
        if not near_nodes:
            return None
        
        min_cost = float('inf')
        best_parent = None
        
        for near_node in near_nodes:
            if self.is_path_collision_free(near_node, new_node):
                cost = near_node.cost + self.distance(near_node, new_node)
                if cost < min_cost:
                    min_cost = cost
                    best_parent = near_node
        
        if best_parent:
            new_node.parent = best_parent
            new_node.cost = min_cost
        
        return best_parent
    
    def rewire(self, new_node: Node, near_nodes: List[Node]):
        """重连附近节点"""
        for near_node in near_nodes:
            if near_node == new_node.parent:
                continue
            
            new_cost = new_node.cost + self.distance(new_node, near_node)
            if new_cost < near_node.cost:
                if self.is_path_collision_free(new_node, near_node):
                    near_node.parent = new_node
                    near_node.cost = new_cost
                    # 递归更新子节点代价
                    self.update_cost(near_node)
    
    def update_cost(self, node: Node):
        """递归更新节点及其子节点的代价"""
        for child_node in self.node_list:
            if child_node.parent == node:
                child_node.cost = node.cost + self.distance(node, child_node)
                self.update_cost(child_node)
    
    def plan(self) -> Optional[List[Tuple[int, int]]]:
        """RRT*路径规划"""
        print(f"开始RRT*规划: 最大迭代{self.max_iter}次")
        
        for i in range(self.max_iter):
            self.iterations_used = i + 1
            
            # 1. 随机采样
            sample_node = self.sample_random_node()
            
            # 2. 找到最近节点
            nearest_node = self.find_nearest_node(sample_node)
            
            # 3. 扩展
            new_node = self.steer(nearest_node, sample_node)
            
            # 4. 碰撞检测
            if not self.is_collision_free(new_node):
                continue
            
            # 5. 找到附近节点
            near_nodes = self.find_near_nodes(new_node)
            
            # 6. 选择最优父节点
            best_parent = self.choose_parent(new_node, near_nodes)
            if best_parent is None:
                continue
            
            # 7. 添加到树中
            self.node_list.append(new_node)
            
            # 8. 重连
            self.rewire(new_node, near_nodes)
            
            # 9. 检查是否到达目标
            if self.is_goal_reached(new_node):
                # 尝试直接连接到目标
                goal_node = Node(self.goal.x, self.goal.y, new_node)
                if self.is_path_collision_free(new_node, goal_node):
                    goal_node.cost = new_node.cost + self.distance(new_node, goal_node)
                    self.node_list.append(goal_node)
                    
                    print(f"RRT*找到路径! 迭代次数: {i+1}")
                    self.path_found = True
                    self.final_path = self.extract_path(goal_node)
                    return self.final_path
            
            # 打印进度
            if (i + 1) % 100 == 0:
                print(f"RRT*迭代: {i+1}/{self.max_iter}")
        
        print("RRT*未找到路径")
        return None
    
    def visualize(self, show_tree: bool = True):
        """可视化结果"""
        plt.figure(figsize=(10, 8))
        
        # 显示地图
        plt.imshow(self.grid_map, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 显示树
        if show_tree:
            for node in self.node_list[1:]:  # 跳过起点
                if node.parent:
                    plt.plot([node.parent.x, node.x], [node.parent.y, node.y], 
                            'b-', alpha=0.3, linewidth=0.5)
        
        # 显示路径
        if self.final_path:
            path_array = np.array(self.final_path)
            plt.plot(path_array[:, 1], path_array[:, 0], 'r-', linewidth=3, label='RRT* Path')
        
        # 显示起点和终点
        plt.plot(self.start.x, self.start.y, 'go', markersize=10, label='Start')
        plt.plot(self.goal.x, self.goal.y, 'ro', markersize=10, label='Goal')
        
        plt.title(f'RRT*路径规划 (迭代: {self.iterations_used})')
        plt.legend()
        plt.axis('equal')
        plt.grid(True, alpha=0.3)
        plt.show()

def test_rrt_algorithms():
    """测试RRT和RRT*算法"""
    # 创建测试环境
    grid = np.zeros((50, 50))
    
    # 添加障碍物
    grid[10:40, 20] = 1  # 垂直墙
    grid[25, 10:40] = 1  # 水平墙
    grid[25, 18:22] = 0  # 在墙中间留个缺口
    
    start = (5, 5)
    goal = (45, 45)
    
    print("测试RRT和RRT*算法")
    print(f"环境大小: {grid.shape}")
    print(f"起点: {start}, 终点: {goal}")
    
    # 测试RRT
    print("\n=== 测试RRT ===")
    rrt = RRTPathPlanner(start, goal, grid, max_iter=500)
    rrt_path = rrt.plan()
    if rrt_path:
        print(f"RRT路径长度: {len(rrt_path)}")
        rrt.visualize()
    
    # 测试RRT*
    print("\n=== 测试RRT* ===")
    rrt_star = RRTStarPathPlanner(start, goal, grid, max_iter=500)
    rrt_star_path = rrt_star.plan()
    if rrt_star_path:
        print(f"RRT*路径长度: {len(rrt_star_path)}")
        rrt_star.visualize()

if __name__ == "__main__":
    test_rrt_algorithms()
