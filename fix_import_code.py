"""
修复导入问题的代码片段
直接复制这段代码到您的主文件中替换导入部分
"""

import sys
import os

def setup_python_motion_planning_path():
    """设置python_motion_planning路径"""
    print("🔧 设置python_motion_planning路径...")
    
    # 多种路径尝试方式
    possible_paths = [
        'python_motion_planning-master/src',
        './python_motion_planning-master/src', 
        '../python_motion_planning-master/src',
        'python_motion_planning-master\\src',
        r'D:\代码\ICE\海冰分割路径规划论文代码20250416\python_motion_planning-master\src',  # 绝对路径
    ]
    
    # 尝试获取当前目录
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
    except NameError:
        current_dir = os.getcwd()
    
    print(f"📁 当前目录: {current_dir}")
    
    # 尝试不同的路径
    motion_planning_path = None
    for path in possible_paths:
        if os.path.isabs(path):
            test_path = path
        else:
            test_path = os.path.join(current_dir, path)
        
        print(f"🔍 测试路径: {test_path}")
        
        if os.path.exists(test_path):
            motion_planning_path = test_path
            print(f"✅ 找到路径: {motion_planning_path}")
            break
    
    if motion_planning_path is None:
        print("❌ 未找到python_motion_planning-master路径")
        return False
    
    # 添加到sys.path
    if motion_planning_path not in sys.path:
        sys.path.insert(0, motion_planning_path)  # 使用insert(0, ...)确保优先级
        print(f"✅ 路径已添加到sys.path")
    
    return True

def test_imports():
    """测试导入"""
    print("\n🧪 测试导入...")
    
    try:
        from python_motion_planning.global_planner.evolutionary_search.aco import ACO
        from python_motion_planning.global_planner.evolutionary_search.pso import PSO
        from python_motion_planning.global_planner.sample_search.rrt import RRT
        from python_motion_planning.global_planner.sample_search.rrt_star import RRTStar
        from python_motion_planning.utils import Env, Grid, Node
        print("✅ 成功导入python_motion_planning算法")
        return True, (ACO, PSO, RRT, RRTStar, Env, Grid, Node)
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False, None

# ===================== 完整的修复代码 =====================
def get_fixed_import_code():
    """返回修复后的完整导入代码"""
    return '''
# ===================== 修复后的导入代码 =====================
import sys
import os

# 设置python_motion_planning路径
def setup_motion_planning():
    possible_paths = [
        'python_motion_planning-master/src',
        './python_motion_planning-master/src', 
        '../python_motion_planning-master/src',
        'python_motion_planning-master\\\\src',
    ]
    
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
    except NameError:
        current_dir = os.getcwd()
    
    for path in possible_paths:
        test_path = os.path.join(current_dir, path)
        if os.path.exists(test_path):
            if test_path not in sys.path:
                sys.path.insert(0, test_path)
            return True
    return False

# 尝试导入
PYTHON_MOTION_PLANNING_AVAILABLE = False
if setup_motion_planning():
    try:
        from python_motion_planning.global_planner.evolutionary_search.aco import ACO
        from python_motion_planning.global_planner.evolutionary_search.pso import PSO
        from python_motion_planning.global_planner.sample_search.rrt import RRT
        from python_motion_planning.global_planner.sample_search.rrt_star import RRTStar
        from python_motion_planning.utils import Env, Grid, Node
        print("✅ 成功导入python_motion_planning算法")
        PYTHON_MOTION_PLANNING_AVAILABLE = True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")

# 备用实现
if not PYTHON_MOTION_PLANNING_AVAILABLE:
    print("⚠️ 使用备用实现")
    class ACO:
        def __init__(self, start, goal, env): pass
        def run(self): return None, None
    class PSO:
        def __init__(self, start, goal, env): pass  
        def run(self): return None, None
    class RRT:
        def __init__(self, start, goal, env): pass
        def run(self): return None, None
    class RRTStar:
        def __init__(self, start, goal, env): pass
        def run(self): return None, None
    class Grid:
        def __init__(self, x, y): 
            self.x_range, self.y_range = x, y
            self.obstacles = set()
        def update(self, obs): self.obstacles = obs
'''

if __name__ == "__main__":
    print("🚀 开始修复导入问题")
    print("=" * 60)
    
    # 设置路径
    if setup_python_motion_planning_path():
        # 测试导入
        success, modules = test_imports()
        if success:
            print("\n🎉 导入修复成功！")
            print("✅ 所有模块导入正常")
            print("✅ 可以在主文件中使用")
        else:
            print("\n❌ 导入仍然失败")
    else:
        print("\n❌ 路径设置失败")
    
    print("\n📝 修复后的完整代码:")
    print(get_fixed_import_code())
    
    print("\n💡 使用方法:")
    print("1. 复制上面的修复代码")
    print("2. 替换主文件中的导入部分")
    print("3. 运行主文件测试")
