"""
测试RRT修复
"""
import sys
import os
import numpy as np

# 添加路径
current_dir = os.getcwd()
motion_planning_path = os.path.join(current_dir, 'python_motion_planning-master', 'src')
if motion_planning_path not in sys.path:
    sys.path.insert(0, motion_planning_path)

def test_environment_creation():
    """测试环境创建"""
    print("🧪 测试环境创建")
    print("=" * 50)
    
    # 导入必要的类
    from python_motion_planning.utils import Grid, Map
    
    # 创建测试网格地图
    grid_map = np.zeros((20, 20))
    grid_map[5:15, 8] = 1  # 垂直墙
    grid_map[10, 5:15] = 1  # 水平墙
    grid_map[10, 6:8] = 0   # 缺口
    
    print(f"测试地图大小: {grid_map.shape}")
    print(f"障碍物数量: {np.sum(grid_map)}")
    
    # 测试Grid创建
    try:
        x_range = grid_map.shape[1]
        y_range = grid_map.shape[0]
        
        grid = Grid(x_range, y_range)
        print(f"✅ Grid创建成功: {grid.x_range}x{grid.y_range}")
        
        # 添加障碍物
        obstacles = set()
        for i in range(y_range):
            for j in range(x_range):
                if grid_map[i, j] == 1:
                    obstacles.add((j, i))
        
        grid.update(obstacles.union(grid.obstacles))
        print(f"✅ Grid障碍物更新成功，总数: {len(grid.obstacles)}")
        
    except Exception as e:
        print(f"❌ Grid创建失败: {e}")
        return False
    
    # 测试Map创建
    try:
        map_env = Map(x_range, y_range)
        print(f"✅ Map创建成功: {map_env.x_range}x{map_env.y_range}")
        
        # 转换障碍物为矩形
        obs_rect = []
        for i in range(y_range):
            for j in range(x_range):
                if grid_map[i, j] == 1:
                    obs_rect.append([j, i, 1, 1])
        
        map_env.update(obs_rect=obs_rect)
        print(f"✅ Map障碍物更新成功，矩形数量: {len(map_env.obs_rect)}")
        print(f"✅ Map具有必要属性: obs_circ={len(map_env.obs_circ)}, obs_rect={len(map_env.obs_rect)}")
        
    except Exception as e:
        print(f"❌ Map创建失败: {e}")
        return False
    
    return True

def test_algorithm_creation():
    """测试算法创建"""
    print("\n🤖 测试算法创建")
    print("=" * 50)
    
    # 导入算法
    try:
        from python_motion_planning.global_planner.evolutionary_search.aco import ACO
        from python_motion_planning.global_planner.evolutionary_search.pso import PSO
        from python_motion_planning.global_planner.sample_search.rrt import RRT
        from python_motion_planning.global_planner.sample_search.rrt_star import RRTStar
        from python_motion_planning.utils import Grid, Map
        
        # 创建测试环境
        grid_map = np.zeros((10, 10))
        grid_map[3:7, 5] = 1  # 简单障碍物
        
        # 创建Grid环境（用于ACO/PSO）
        grid = Grid(10, 10)
        obstacles = set()
        for i in range(10):
            for j in range(10):
                if grid_map[i, j] == 1:
                    obstacles.add((j, i))
        grid.update(obstacles.union(grid.obstacles))
        
        # 创建Map环境（用于RRT/RRT*）
        map_env = Map(10, 10)
        obs_rect = []
        for i in range(10):
            for j in range(10):
                if grid_map[i, j] == 1:
                    obs_rect.append([j, i, 1, 1])
        map_env.update(obs_rect=obs_rect)
        
        start = (1, 1)
        goal = (8, 8)
        
        # 测试ACO
        aco = ACO(start, goal, grid)
        print("✅ ACO对象创建成功")
        
        # 测试PSO
        pso = PSO(start, goal, grid)
        print("✅ PSO对象创建成功")
        
        # 测试RRT
        rrt = RRT(start, goal, map_env)
        print("✅ RRT对象创建成功")
        
        # 测试RRT*
        rrt_star = RRTStar(start, goal, map_env)
        print("✅ RRT*对象创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始RRT修复测试")
    print("=" * 60)
    
    # 测试环境创建
    if not test_environment_creation():
        print("\n❌ 环境创建测试失败")
        return
    
    # 测试算法创建
    if not test_algorithm_creation():
        print("\n❌ 算法创建测试失败")
        return
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("✅ Grid和Map环境创建正常")
    print("✅ ACO/PSO使用Grid环境")
    print("✅ RRT/RRT*使用Map环境")
    print("✅ 修复了AttributeError: 'Grid' object has no attribute 'obs_circ'")
    print("✅ 现在可以正常运行所有算法")
    print("=" * 60)

if __name__ == "__main__":
    main()
