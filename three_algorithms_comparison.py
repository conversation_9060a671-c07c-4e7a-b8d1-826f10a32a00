"""
两种群体智能算法对比：ACO vs PSO
"""
import numpy as np
import matplotlib.pyplot as plt
import time
from ImprovedACO_PathPlanning import ACOPathPlanner
from PSO_PathPlanning import PSOPathPlanner

def create_test_environment():
    """创建标准测试环境"""
    grid = np.zeros((25, 25))
    
    # 添加复杂障碍物
    grid[5:20, 10] = 1    # 垂直墙
    grid[12, 5:20] = 1    # 水平墙
    grid[3:7, 3:7] = 1    # 左上角块
    grid[18:22, 18:22] = 1  # 右下角块
    
    # 添加一些随机障碍物
    np.random.seed(42)
    for _ in range(8):
        x, y = np.random.randint(0, 25, 2)
        if grid[x, y] == 0:
            grid[x:x+2, y:y+2] = 1
    
    start = (2, 2)
    goal = (22, 22)
    
    return grid, start, goal

def benchmark_algorithm(algorithm_name, algorithm_class, grid, start, goal, **kwargs):
    """测试单个算法性能"""
    print(f"\n测试 {algorithm_name}...")
    
    results = []
    times = []
    paths = []
    
    # 运行多次测试
    num_runs = 5
    
    for run in range(num_runs):
        print(f"  运行 {run + 1}/{num_runs}")
        
        start_time = time.time()
        
        try:
            if algorithm_name == "ACO":
                planner = algorithm_class(start, goal, grid, **kwargs)
                path = planner.optimize()
            else:  # PSO
                planner = algorithm_class(start, goal, grid, **kwargs)
                raw_path = planner.optimize()
                path = planner.get_path_as_points()
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            if path:
                # 计算路径质量
                path_length = len(path)
                reached_goal = (path[-1] == goal)
                
                # 计算实际距离
                actual_distance = 0
                for i in range(len(path) - 1):
                    p1, p2 = path[i], path[i+1]
                    actual_distance += np.linalg.norm(np.array(p2) - np.array(p1))
                
                results.append({
                    'success': True,
                    'time': execution_time,
                    'path_length': path_length,
                    'actual_distance': actual_distance,
                    'reached_goal': reached_goal
                })
                paths.append(path)
                
                print(f"    ✅ 成功: {execution_time:.2f}s, 长度: {path_length}, 距离: {actual_distance:.2f}")
            else:
                results.append({
                    'success': False,
                    'time': execution_time,
                    'path_length': 0,
                    'actual_distance': float('inf'),
                    'reached_goal': False
                })
                paths.append(None)
                print(f"    ❌ 失败: {execution_time:.2f}s")
                
        except Exception as e:
            print(f"    💥 错误: {e}")
            results.append({
                'success': False,
                'time': float('inf'),
                'path_length': 0,
                'actual_distance': float('inf'),
                'reached_goal': False
            })
            paths.append(None)
    
    return results, paths

def analyze_results(results_dict):
    """分析结果"""
    analysis = {}
    
    for alg_name, results in results_dict.items():
        successful_runs = [r for r in results if r['success']]
        
        if successful_runs:
            analysis[alg_name] = {
                'success_rate': len(successful_runs) / len(results),
                'avg_time': np.mean([r['time'] for r in successful_runs]),
                'avg_path_length': np.mean([r['path_length'] for r in successful_runs]),
                'avg_distance': np.mean([r['actual_distance'] for r in successful_runs]),
                'std_time': np.std([r['time'] for r in successful_runs]),
                'min_time': np.min([r['time'] for r in successful_runs]),
                'max_time': np.max([r['time'] for r in successful_runs])
            }
        else:
            analysis[alg_name] = {
                'success_rate': 0,
                'avg_time': float('inf'),
                'avg_path_length': 0,
                'avg_distance': float('inf'),
                'std_time': 0,
                'min_time': float('inf'),
                'max_time': float('inf')
            }
    
    return analysis

def visualize_comparison(grid, start, goal, paths_dict, analysis):
    """可视化对比结果"""
    fig = plt.figure(figsize=(20, 15))
    
    # 1. 路径对比 (2x2)
    algorithms = list(paths_dict.keys())
    colors = ['red', 'blue', 'green']
    
    for i, (alg_name, paths) in enumerate(paths_dict.items()):
        plt.subplot(3, 3, i+1)
        
        # 显示地图
        plt.imshow(grid, cmap='gray_r', origin='upper', alpha=0.7)
        
        # 绘制所有成功的路径
        successful_paths = [p for p in paths if p is not None]
        
        if successful_paths:
            for j, path in enumerate(successful_paths):
                path_array = np.array(path)
                alpha = 0.3 + 0.7 * (j == 0)  # 第一条路径更明显
                plt.plot(path_array[:, 1], path_array[:, 0], 
                        color=colors[i], linewidth=2, alpha=alpha)
        
        # 标记起点和终点
        plt.plot(start[1], start[0], 'go', markersize=12, label='Start')
        plt.plot(goal[1], goal[0], 'ro', markersize=12, label='Goal')
        
        plt.title(f'{alg_name}\n成功率: {analysis[alg_name]["success_rate"]*100:.0f}%')
        plt.legend()
        plt.axis('off')
    
    # 2. 性能对比图表
    # 执行时间对比
    plt.subplot(3, 3, 4)
    alg_names = list(analysis.keys())
    times = [analysis[alg]['avg_time'] for alg in alg_names]
    time_stds = [analysis[alg]['std_time'] for alg in alg_names]
    
    bars = plt.bar(alg_names, times, yerr=time_stds, capsize=5, 
                   color=colors[:len(alg_names)], alpha=0.7)
    plt.ylabel('平均执行时间 (秒)')
    plt.title('执行时间对比')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, time_val in zip(bars, times):
        if time_val != float('inf'):
            plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.1,
                    f'{time_val:.2f}s', ha='center', va='bottom')
    
    # 路径长度对比
    plt.subplot(3, 3, 5)
    path_lengths = [analysis[alg]['avg_path_length'] for alg in alg_names]
    
    bars = plt.bar(alg_names, path_lengths, color=colors[:len(alg_names)], alpha=0.7)
    plt.ylabel('平均路径长度')
    plt.title('路径长度对比')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    for bar, length in zip(bars, path_lengths):
        if length > 0:
            plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.5,
                    f'{length:.0f}', ha='center', va='bottom')
    
    # 成功率对比
    plt.subplot(3, 3, 6)
    success_rates = [analysis[alg]['success_rate'] * 100 for alg in alg_names]
    
    bars = plt.bar(alg_names, success_rates, color=colors[:len(alg_names)], alpha=0.7)
    plt.ylabel('成功率 (%)')
    plt.title('成功率对比')
    plt.xticks(rotation=45)
    plt.ylim(0, 110)
    plt.grid(True, alpha=0.3)
    
    for bar, rate in zip(bars, success_rates):
        plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 2,
                f'{rate:.0f}%', ha='center', va='bottom')
    
    # 雷达图对比
    plt.subplot(3, 3, 7, projection='polar')
    
    categories = ['速度', '路径质量', '成功率', '稳定性']
    angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]
    
    for i, alg_name in enumerate(alg_names):
        # 归一化指标 (0-1)
        speed_score = 1 - min(1, analysis[alg_name]['avg_time'] / 10)  # 速度越快分数越高
        quality_score = 1 / (analysis[alg_name]['avg_path_length'] + 1) if analysis[alg_name]['avg_path_length'] > 0 else 0
        success_score = analysis[alg_name]['success_rate']
        stability_score = 1 - min(1, analysis[alg_name]['std_time'] / 5)  # 标准差越小越稳定
        
        values = [speed_score, quality_score, success_score, stability_score]
        values += values[:1]
        
        plt.plot(angles, values, 'o-', linewidth=2, label=alg_name, color=colors[i])
        plt.fill(angles, values, alpha=0.25, color=colors[i])
    
    plt.xticks(angles[:-1], categories)
    plt.ylim(0, 1)
    plt.title('综合性能雷达图')
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    # 详细统计表
    plt.subplot(3, 3, 8)
    plt.axis('off')
    
    table_data = []
    for alg_name in alg_names:
        stats = analysis[alg_name]
        table_data.append([
            alg_name,
            f"{stats['avg_time']:.2f}±{stats['std_time']:.2f}",
            f"{stats['avg_path_length']:.0f}",
            f"{stats['success_rate']*100:.0f}%"
        ])
    
    table = plt.table(cellText=table_data,
                     colLabels=['算法', '时间(s)', '路径长度', '成功率'],
                     cellLoc='center',
                     loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(table_data[0])):
        table[(0, i)].set_facecolor('#4ECDC4')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    plt.title('性能统计表', fontsize=14, fontweight='bold')
    
    # 算法特点说明
    plt.subplot(3, 3, 9)
    plt.axis('off')
    
    descriptions = {
        'ACO': '• 改进蚁群算法\n• 启发式移动\n• 障碍物避让\n• 适应性强',
        'PSO': '• 粒子群优化\n• 收敛快速\n• 实现简单\n• 适合实时应用'
    }
    
    y_pos = 0.9
    for i, (alg_name, desc) in enumerate(descriptions.items()):
        if alg_name in alg_names:
            plt.text(0.02, y_pos, f"{alg_name}:", fontsize=12, fontweight='bold', 
                    color=colors[list(alg_names).index(alg_name)], transform=plt.gca().transAxes)
            plt.text(0.02, y_pos-0.15, desc, fontsize=10, 
                    transform=plt.gca().transAxes)
            y_pos -= 0.35
    
    plt.title('算法特点', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.show()

def main():
    """主函数"""
    print("🔬 ACO vs PSO 群体智能算法性能对比")
    print("=" * 50)
    
    # 创建测试环境
    grid, start, goal = create_test_environment()
    print(f"测试环境: {grid.shape}")
    print(f"起点: {start}, 终点: {goal}")
    print(f"障碍物数量: {np.sum(grid)}")
    
    # 算法配置
    algorithms = {
        "ACO": {
            'class': ACOPathPlanner,
            'params': {'num_ants': 15, 'max_iterations': 30, 'step_size': 0.8}
        },
        "PSO": {
            'class': PSOPathPlanner,
            'params': {'n_particles': 20, 'max_iter': 50}
        }
    }
    
    # 运行测试
    all_results = {}
    all_paths = {}
    
    for alg_name, config in algorithms.items():
        results, paths = benchmark_algorithm(
            alg_name, 
            config['class'], 
            grid, start, goal, 
            **config['params']
        )
        all_results[alg_name] = results
        all_paths[alg_name] = paths
    
    # 分析结果
    analysis = analyze_results(all_results)
    
    # 可视化对比
    visualize_comparison(grid, start, goal, all_paths, analysis)
    
    # 打印总结
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    
    for alg_name, stats in analysis.items():
        print(f"\n{alg_name}:")
        print(f"  成功率: {stats['success_rate']*100:.1f}%")
        print(f"  平均时间: {stats['avg_time']:.2f}±{stats['std_time']:.2f}秒")
        print(f"  平均路径长度: {stats['avg_path_length']:.1f}")
    
    # 推荐
    print(f"\n💡 推荐:")
    fastest_alg = min(analysis.keys(), key=lambda x: analysis[x]['avg_time'])
    most_reliable = max(analysis.keys(), key=lambda x: analysis[x]['success_rate'])
    
    print(f"  最快算法: {fastest_alg}")
    print(f"  最可靠算法: {most_reliable}")
    
    if analysis['ACO']['avg_time'] < analysis['PSO']['avg_time']:
        print(f"  ACO相比PSO速度: {'更快' if analysis['ACO']['avg_time'] < analysis['PSO']['avg_time'] else '更慢'}")
    else:
        print(f"  PSO相比ACO速度提升: {(analysis['ACO']['avg_time'] / analysis['PSO']['avg_time'] - 1)*100:.1f}%")

if __name__ == "__main__":
    main()
