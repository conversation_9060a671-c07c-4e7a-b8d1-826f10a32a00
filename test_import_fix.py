"""
测试导入修复
"""
import sys
import os
import numpy as np

# 添加python_motion_planning-master到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'python_motion_planning-master', 'src'))

def test_imports():
    """测试导入"""
    print("🧪 测试python_motion_planning导入修复")
    print("=" * 50)
    
    try:
        from python_motion_planning.global_planner.evolutionary_search.aco import ACO
        print("✅ ACO导入成功")
    except ImportError as e:
        print(f"❌ ACO导入失败: {e}")
        return False
    
    try:
        from python_motion_planning.global_planner.evolutionary_search.pso import PSO
        print("✅ PSO导入成功")
    except ImportError as e:
        print(f"❌ PSO导入失败: {e}")
        return False
    
    try:
        from python_motion_planning.global_planner.sample_search.rrt import RRT
        print("✅ RRT导入成功")
    except ImportError as e:
        print(f"❌ RRT导入失败: {e}")
        return False
    
    try:
        from python_motion_planning.global_planner.sample_search.rrt_star import RRTStar
        print("✅ RRT*导入成功")
    except ImportError as e:
        print(f"❌ RRT*导入失败: {e}")
        return False
    
    try:
        from python_motion_planning.utils import Env, Grid, Node
        print("✅ Utils (Env, Grid, Node)导入成功")
    except ImportError as e:
        print(f"❌ Utils导入失败: {e}")
        return False
    
    return True

def test_grid_creation():
    """测试Grid对象创建"""
    print("\n🔧 测试Grid对象创建")
    print("=" * 50)
    
    try:
        from python_motion_planning.utils import Grid
        
        # 创建简单的Grid
        grid = Grid(20, 20)
        print(f"✅ Grid对象创建成功: {grid.x_range}x{grid.y_range}")
        
        # 添加障碍物
        grid.add_obstacle((5, 5))
        grid.add_obstacle((10, 10))
        print("✅ 障碍物添加成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Grid对象创建失败: {e}")
        return False

def test_algorithm_creation():
    """测试算法对象创建"""
    print("\n🤖 测试算法对象创建")
    print("=" * 50)
    
    try:
        from python_motion_planning.utils import Grid
        from python_motion_planning.global_planner.evolutionary_search.aco import ACO
        from python_motion_planning.global_planner.evolutionary_search.pso import PSO
        from python_motion_planning.global_planner.sample_search.rrt import RRT
        from python_motion_planning.global_planner.sample_search.rrt_star import RRTStar
        
        # 创建Grid
        grid = Grid(20, 20)
        grid.add_obstacle((10, 10))
        
        start = (2, 2)
        goal = (18, 18)
        
        # 测试ACO
        aco = ACO(start, goal, grid)
        print("✅ ACO对象创建成功")
        
        # 测试PSO
        pso = PSO(start, goal, grid)
        print("✅ PSO对象创建成功")
        
        # 测试RRT
        rrt = RRT(start, goal, grid)
        print("✅ RRT对象创建成功")
        
        # 测试RRT*
        rrt_star = RRTStar(start, goal, grid)
        print("✅ RRT*对象创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法对象创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始导入修复测试")
    print("=" * 60)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请检查python_motion_planning-master路径")
        return
    
    # 测试Grid创建
    if not test_grid_creation():
        print("\n❌ Grid创建测试失败")
        return
    
    # 测试算法创建
    if not test_algorithm_creation():
        print("\n❌ 算法创建测试失败")
        return
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！导入修复成功")
    print("✅ python_motion_planning-master集成正常")
    print("✅ 可以运行主文件进行完整测试")
    print("=" * 60)

if __name__ == "__main__":
    main()
