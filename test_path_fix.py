"""
测试路径修复和导入
"""
import sys
import os

def test_path_setup():
    """测试路径设置"""
    print("🔧 测试路径设置")
    print("=" * 50)
    
    # 获取当前目录
    try:
        current_dir = os.path.dirname(__file__)
        print(f"使用__file__: {current_dir}")
    except NameError:
        current_dir = os.getcwd()
        print(f"使用当前工作目录: {current_dir}")
    
    # 构建路径
    motion_planning_path = os.path.join(current_dir, 'python_motion_planning-master', 'src')
    print(f"目标路径: {motion_planning_path}")
    
    # 检查路径是否存在
    if os.path.exists(motion_planning_path):
        print("✅ 路径存在")
    else:
        print("❌ 路径不存在")
        return False
    
    # 检查关键文件
    key_files = [
        'python_motion_planning/__init__.py',
        'python_motion_planning/utils/__init__.py',
        'python_motion_planning/global_planner/evolutionary_search/aco.py',
        'python_motion_planning/global_planner/evolutionary_search/pso.py',
        'python_motion_planning/global_planner/sample_search/rrt.py',
        'python_motion_planning/global_planner/sample_search/rrt_star.py'
    ]
    
    for file_path in key_files:
        full_path = os.path.join(motion_planning_path, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            return False
    
    # 添加到sys.path
    if motion_planning_path not in sys.path:
        sys.path.append(motion_planning_path)
        print(f"✅ 路径已添加到sys.path")
    
    return True

def test_imports():
    """测试导入"""
    print("\n🧪 测试导入")
    print("=" * 50)
    
    try:
        import python_motion_planning
        print("✅ python_motion_planning基础模块导入成功")
    except ImportError as e:
        print(f"❌ python_motion_planning基础模块导入失败: {e}")
        return False
    
    try:
        from python_motion_planning.utils import Env, Grid, Node
        print("✅ utils模块导入成功")
    except ImportError as e:
        print(f"❌ utils模块导入失败: {e}")
        return False
    
    try:
        from python_motion_planning.global_planner.evolutionary_search.aco import ACO
        print("✅ ACO导入成功")
    except ImportError as e:
        print(f"❌ ACO导入失败: {e}")
        return False
    
    try:
        from python_motion_planning.global_planner.evolutionary_search.pso import PSO
        print("✅ PSO导入成功")
    except ImportError as e:
        print(f"❌ PSO导入失败: {e}")
        return False
    
    try:
        from python_motion_planning.global_planner.sample_search.rrt import RRT
        print("✅ RRT导入成功")
    except ImportError as e:
        print(f"❌ RRT导入失败: {e}")
        return False
    
    try:
        from python_motion_planning.global_planner.sample_search.rrt_star import RRTStar
        print("✅ RRT*导入成功")
    except ImportError as e:
        print(f"❌ RRT*导入失败: {e}")
        return False
    
    return True

def test_grid_creation():
    """测试Grid创建"""
    print("\n🏗️ 测试Grid创建")
    print("=" * 50)
    
    try:
        from python_motion_planning.utils import Grid
        
        # 创建Grid
        grid = Grid(20, 20)
        print(f"✅ Grid创建成功: {grid.x_range}x{grid.y_range}")
        
        # 测试障碍物
        obstacles = {(5, 5), (10, 10), (15, 15)}
        grid.update(obstacles.union(grid.obstacles))
        print(f"✅ 障碍物更新成功，总数: {len(grid.obstacles)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Grid创建失败: {e}")
        return False

def create_fixed_import_code():
    """生成修复后的导入代码"""
    print("\n📝 生成修复后的导入代码")
    print("=" * 50)
    
    code = '''
# ===================== 修复后的导入代码 =====================
import sys
import os

# 路径设置（兼容脚本和Jupyter）
try:
    current_dir = os.path.dirname(__file__)
except NameError:
    current_dir = os.getcwd()

motion_planning_path = os.path.join(current_dir, 'python_motion_planning-master', 'src')
if motion_planning_path not in sys.path:
    sys.path.append(motion_planning_path)

# 导入算法
try:
    from python_motion_planning.global_planner.evolutionary_search.aco import ACO
    from python_motion_planning.global_planner.evolutionary_search.pso import PSO
    from python_motion_planning.global_planner.sample_search.rrt import RRT
    from python_motion_planning.global_planner.sample_search.rrt_star import RRTStar
    from python_motion_planning.utils import Env, Grid, Node
    print("✅ 成功导入python_motion_planning算法")
    PYTHON_MOTION_PLANNING_AVAILABLE = True
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    PYTHON_MOTION_PLANNING_AVAILABLE = False
'''
    
    print(code)
    return code

def main():
    """主测试函数"""
    print("🚀 开始路径和导入修复测试")
    print("=" * 60)
    
    # 测试路径设置
    if not test_path_setup():
        print("\n❌ 路径设置失败")
        return
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败")
        return
    
    # 测试Grid创建
    if not test_grid_creation():
        print("\n❌ Grid创建失败")
        return
    
    # 生成修复代码
    create_fixed_import_code()
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！")
    print("✅ 路径设置正确")
    print("✅ 导入功能正常")
    print("✅ Grid对象创建成功")
    print("✅ 可以在主文件中使用修复后的代码")
    print("=" * 60)

if __name__ == "__main__":
    main()
