import numpy as np
import matplotlib.pyplot as plt
import time
from DeepRL_PathPlanning import DeepQLearningPathPlanner, SimpleQLearningPathPlanner
from AdvancedRL_PathPlanning import PPOPathPlanner, A3CPathPlanner

def create_test_environment():
    """创建测试环境"""
    # 创建一个简单的测试环境
    grid_size = 50
    grid = np.zeros((grid_size, grid_size))
    
    # 添加一些障碍物
    # 垂直障碍
    grid[10:40, 20] = 1
    grid[10:40, 30] = 1
    
    # 水平障碍
    grid[25, 5:20] = 1
    grid[25, 30:45] = 1
    
    # 随机障碍
    np.random.seed(42)
    for _ in range(20):
        x, y = np.random.randint(0, grid_size, 2)
        if grid[x, y] == 0:  # 确保不覆盖现有障碍
            grid[x, y] = 1
    
    return grid

def run_algorithm_comparison():
    """运行算法对比实验"""
    print("=== 深度强化学习算法性能对比实验 ===")
    
    # 创建测试环境
    test_grid = create_test_environment()
    start = (5, 5)
    goal = (45, 45)
    
    # 算法配置
    algorithms = {
        'Q-Learning': {
            'class': SimpleQLearningPathPlanner,
            'params': {'max_episodes': 200},
            'train_params': {}
        },
        'DQN': {
            'class': DeepQLearningPathPlanner,
            'params': {
                'learning_rate': 1e-4,
                'gamma': 0.99,
                'epsilon_start': 1.0,
                'epsilon_end': 0.01,
                'epsilon_decay': 0.995,
                'memory_size': 5000,
                'batch_size': 32,
                'target_update': 50,
                'use_dueling': True,
                'use_prioritized_replay': True
            },
            'train_params': {'max_episodes': 150, 'max_steps_per_episode': 300}
        },
        'PPO': {
            'class': PPOPathPlanner,
            'params': {
                'learning_rate': 3e-4,
                'gamma': 0.99,
                'gae_lambda': 0.95,
                'clip_epsilon': 0.2,
                'entropy_coef': 0.01,
                'value_coef': 0.5,
                'max_grad_norm': 0.5,
                'ppo_epochs': 4,
                'batch_size': 32
            },
            'train_params': {'max_episodes': 150, 'max_steps_per_episode': 300, 'update_frequency': 64}
        },
        'A3C': {
            'class': A3CPathPlanner,
            'params': {'learning_rate': 1e-4},
            'train_params': {'max_episodes': 150, 'max_steps_per_episode': 200}
        }
    }
    
    results = {}
    
    for alg_name, config in algorithms.items():
        print(f"\n--- 测试 {alg_name} 算法 ---")
        
        try:
            # 创建规划器
            planner = config['class'](start, goal, test_grid, **config['params'])
            
            # 训练
            start_time = time.time()
            planner.train(**config['train_params'])
            training_time = time.time() - start_time
            
            # 路径规划
            start_time = time.time()
            path = planner.plan_path()
            planning_time = time.time() - start_time
            
            # 记录结果
            results[alg_name] = {
                'planner': planner,
                'path': path,
                'training_time': training_time,
                'planning_time': planning_time,
                'path_length': len(path) if path else float('inf'),
                'success': path is not None
            }
            
            print(f"训练时间: {training_time:.2f}秒")
            print(f"规划时间: {planning_time:.4f}秒")
            print(f"路径长度: {len(path) if path else '未找到路径'}")
            print(f"成功率: {'成功' if path else '失败'}")
            
        except Exception as e:
            print(f"算法 {alg_name} 运行出错: {e}")
            results[alg_name] = {
                'planner': None,
                'path': None,
                'training_time': float('inf'),
                'planning_time': float('inf'),
                'path_length': float('inf'),
                'success': False
            }
    
    return test_grid, start, goal, results

def visualize_comparison_results(test_grid, start, goal, results):
    """可视化对比结果"""
    # 创建子图
    n_algorithms = len(results)
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    algorithm_names = list(results.keys())
    
    for i, (alg_name, result) in enumerate(results.items()):
        if i >= 4:  # 最多显示4个算法
            break
            
        ax = axes[i]
        
        # 显示环境
        ax.imshow(test_grid, cmap='gray_r', origin='upper')
        
        # 绘制路径
        if result['path']:
            path_array = np.array(result['path'])
            ax.plot(path_array[:, 1], path_array[:, 0], 'r-', linewidth=2, label='Path')
            ax.plot(path_array[:, 1], path_array[:, 0], 'ro', markersize=2)
        
        # 绘制起点和终点
        ax.plot(start[1], start[0], 'go', markersize=10, label='Start')
        ax.plot(goal[1], goal[0], 'bo', markersize=10, label='Goal')
        
        # 设置标题和标签
        success_text = "成功" if result['success'] else "失败"
        path_len = result['path_length'] if result['path_length'] != float('inf') else "N/A"
        ax.set_title(f'{alg_name}\n{success_text} | 路径长度: {path_len}')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
    # 隐藏多余的子图
    for i in range(n_algorithms, 4):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.show()
    
    # 性能对比图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 提取数据
    alg_names = []
    training_times = []
    planning_times = []
    path_lengths = []
    success_rates = []
    
    for alg_name, result in results.items():
        alg_names.append(alg_name)
        training_times.append(result['training_time'] if result['training_time'] != float('inf') else 0)
        planning_times.append(result['planning_time'] if result['planning_time'] != float('inf') else 0)
        path_lengths.append(result['path_length'] if result['path_length'] != float('inf') else 0)
        success_rates.append(1 if result['success'] else 0)
    
    # 训练时间对比
    ax1.bar(alg_names, training_times)
    ax1.set_title('训练时间对比')
    ax1.set_ylabel('时间 (秒)')
    ax1.tick_params(axis='x', rotation=45)
    
    # 规划时间对比
    ax2.bar(alg_names, planning_times)
    ax2.set_title('规划时间对比')
    ax2.set_ylabel('时间 (秒)')
    ax2.tick_params(axis='x', rotation=45)
    
    # 路径长度对比
    valid_lengths = [length for length in path_lengths if length > 0]
    valid_names = [name for name, length in zip(alg_names, path_lengths) if length > 0]
    if valid_lengths:
        ax3.bar(valid_names, valid_lengths)
        ax3.set_title('路径长度对比')
        ax3.set_ylabel('路径长度')
        ax3.tick_params(axis='x', rotation=45)
    
    # 成功率对比
    ax4.bar(alg_names, success_rates)
    ax4.set_title('成功率对比')
    ax4.set_ylabel('成功率')
    ax4.set_ylim(0, 1.1)
    ax4.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()

def display_training_curves(results):
    """显示训练曲线"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    axes = axes.flatten()
    
    for i, (alg_name, result) in enumerate(results.items()):
        if i >= 4 or not result['planner']:
            continue
            
        ax = axes[i]
        planner = result['planner']
        
        if hasattr(planner, 'episode_rewards') and planner.episode_rewards:
            ax.plot(planner.episode_rewards)
            ax.set_title(f'{alg_name} - 训练奖励曲线')
            ax.set_xlabel('回合')
            ax.set_ylabel('总奖励')
            ax.grid(True)
        else:
            ax.text(0.5, 0.5, f'{alg_name}\n无训练数据', 
                   ha='center', va='center', transform=ax.transAxes)
    
    # 隐藏多余的子图
    for i in range(len(results), 4):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.show()

def main():
    """主函数"""
    print("开始深度强化学习算法对比实验...")
    
    # 运行对比实验
    test_grid, start, goal, results = run_algorithm_comparison()
    
    # 可视化结果
    print("\n=== 可视化对比结果 ===")
    visualize_comparison_results(test_grid, start, goal, results)
    
    # 显示训练曲线
    print("\n=== 显示训练曲线 ===")
    display_training_curves(results)
    
    # 打印总结
    print("\n=== 实验总结 ===")
    for alg_name, result in results.items():
        print(f"{alg_name}:")
        print(f"  成功: {'是' if result['success'] else '否'}")
        print(f"  训练时间: {result['training_time']:.2f}秒")
        print(f"  规划时间: {result['planning_time']:.4f}秒")
        print(f"  路径长度: {result['path_length'] if result['path_length'] != float('inf') else 'N/A'}")
        print()

if __name__ == "__main__":
    main()
