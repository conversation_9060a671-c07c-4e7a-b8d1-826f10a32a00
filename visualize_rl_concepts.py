"""
可视化强化学习核心概念：损失函数、奖励分布、Epsilon策略
"""
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn.functional as F

def visualize_loss_function():
    """可视化不同损失函数"""
    # 生成预测值和目标值
    predicted = np.linspace(-2, 2, 100)
    target = 0.0
    
    # 计算不同损失
    mse_loss = (predicted - target) ** 2
    mae_loss = np.abs(predicted - target)
    huber_loss = np.where(np.abs(predicted - target) < 1.0,
                         0.5 * (predicted - target) ** 2,
                         np.abs(predicted - target) - 0.5)
    
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.plot(predicted, mse_loss, 'b-', linewidth=2, label='MSE Loss')
    plt.xlabel('预测值 - 目标值')
    plt.ylabel('损失值')
    plt.title('MSE损失函数\n(您的DQN使用)')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.subplot(1, 3, 2)
    plt.plot(predicted, mae_loss, 'r-', linewidth=2, label='MAE Loss')
    plt.xlabel('预测值 - 目标值')
    plt.ylabel('损失值')
    plt.title('MAE损失函数')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.subplot(1, 3, 3)
    plt.plot(predicted, huber_loss, 'g-', linewidth=2, label='Huber Loss')
    plt.xlabel('预测值 - 目标值')
    plt.ylabel('损失值')
    plt.title('Huber损失函数\n(更鲁棒)')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.tight_layout()
    plt.show()

def visualize_reward_distribution():
    """可视化奖励分布"""
    # 模拟您的奖励函数
    def get_reward_simulation(distance_change, collision, goal_reached):
        if goal_reached:
            return 100.0
        if collision:
            return -10.0
        return distance_change * 0.1 - 0.01
    
    # 生成奖励样本
    rewards = []
    scenarios = []
    
    # 正常移动（大部分情况）
    for _ in range(1000):
        distance_change = np.random.normal(0, 0.5)  # 距离变化
        reward = get_reward_simulation(distance_change, False, False)
        rewards.append(reward)
        scenarios.append('正常移动')
    
    # 碰撞情况
    for _ in range(50):
        reward = get_reward_simulation(0, True, False)
        rewards.append(reward)
        scenarios.append('碰撞')
    
    # 到达目标
    for _ in range(10):
        reward = get_reward_simulation(0, False, True)
        rewards.append(reward)
        scenarios.append('到达目标')
    
    plt.figure(figsize=(15, 5))
    
    # 奖励分布直方图
    plt.subplot(1, 3, 1)
    plt.hist(rewards, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.xlabel('奖励值')
    plt.ylabel('频次')
    plt.title('奖励分布直方图')
    plt.grid(True, alpha=0.3)
    
    # 按场景分类的奖励
    plt.subplot(1, 3, 2)
    scenario_rewards = {
        '正常移动': [r for r, s in zip(rewards, scenarios) if s == '正常移动'],
        '碰撞': [r for r, s in zip(rewards, scenarios) if s == '碰撞'],
        '到达目标': [r for r, s in zip(rewards, scenarios) if s == '到达目标']
    }
    
    colors = ['lightblue', 'red', 'green']
    for i, (scenario, reward_list) in enumerate(scenario_rewards.items()):
        plt.scatter([i] * len(reward_list), reward_list, 
                   alpha=0.6, color=colors[i], s=20)
    
    plt.xticks(range(3), scenario_rewards.keys())
    plt.ylabel('奖励值')
    plt.title('不同场景的奖励分布')
    plt.grid(True, alpha=0.3)
    
    # 累积奖励示例
    plt.subplot(1, 3, 3)
    episode_rewards = np.cumsum(np.random.choice(rewards, 200))
    plt.plot(episode_rewards, 'b-', linewidth=2)
    plt.xlabel('步数')
    plt.ylabel('累积奖励')
    plt.title('单回合累积奖励示例')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 打印统计信息
    print("=== 奖励分布统计 ===")
    print(f"奖励范围: [{min(rewards):.3f}, {max(rewards):.3f}]")
    print(f"平均奖励: {np.mean(rewards):.3f}")
    print(f"奖励标准差: {np.std(rewards):.3f}")
    print(f"正常移动奖励范围: [{min(scenario_rewards['正常移动']):.3f}, {max(scenario_rewards['正常移动']):.3f}]")

def visualize_epsilon_strategy():
    """可视化Epsilon策略"""
    episodes = np.arange(0, 1000)
    
    # 不同的epsilon衰减策略
    # 1. 指数衰减（您的实现）
    epsilon_exp = []
    eps = 1.0
    for _ in episodes:
        epsilon_exp.append(eps)
        if eps > 0.01:
            eps *= 0.995
    
    # 2. 线性衰减
    epsilon_linear = np.maximum(0.01, 1.0 - 0.99 * episodes / 1000)
    
    # 3. 分段衰减
    epsilon_step = np.where(episodes < 500, 1.0 - 0.9 * episodes / 500, 0.1)
    
    # 4. 余弦衰减
    epsilon_cosine = 0.01 + 0.99 * 0.5 * (1 + np.cos(np.pi * episodes / 1000))
    
    plt.figure(figsize=(15, 10))
    
    # Epsilon衰减曲线
    plt.subplot(2, 3, 1)
    plt.plot(episodes, epsilon_exp, 'b-', linewidth=2, label='指数衰减 (您的实现)')
    plt.plot(episodes, epsilon_linear, 'r-', linewidth=2, label='线性衰减')
    plt.plot(episodes, epsilon_step, 'g-', linewidth=2, label='分段衰减')
    plt.plot(episodes, epsilon_cosine, 'm-', linewidth=2, label='余弦衰减')
    plt.xlabel('训练回合')
    plt.ylabel('Epsilon值')
    plt.title('不同Epsilon衰减策略')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 探索vs利用比例
    plt.subplot(2, 3, 2)
    eps_current = 0.3  # 当前epsilon值示例
    exploration_prob = eps_current
    exploitation_prob = 1 - eps_current
    
    labels = ['探索 (Exploration)', '利用 (Exploitation)']
    sizes = [exploration_prob, exploitation_prob]
    colors = ['lightcoral', 'lightblue']
    
    plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    plt.title(f'动作选择策略\n(ε = {eps_current})')
    
    # 模拟动作选择过程
    plt.subplot(2, 3, 3)
    np.random.seed(42)
    actions = []
    epsilons = [1.0, 0.5, 0.1, 0.01]
    
    for eps in epsilons:
        episode_actions = []
        for _ in range(100):
            if np.random.random() < eps:
                episode_actions.append('探索')
            else:
                episode_actions.append('利用')
        
        exploration_ratio = episode_actions.count('探索') / len(episode_actions)
        actions.append(exploration_ratio)
    
    plt.bar(range(len(epsilons)), actions, color='orange', alpha=0.7)
    plt.xticks(range(len(epsilons)), [f'ε={eps}' for eps in epsilons])
    plt.ylabel('探索动作比例')
    plt.title('不同Epsilon值的探索比例')
    plt.grid(True, alpha=0.3)
    
    # Q值收敛示例
    plt.subplot(2, 3, 4)
    # 模拟Q值学习过程
    true_q = 10.0
    q_estimates = []
    q_current = 0.0
    learning_rate = 0.1
    
    for i in range(200):
        # 模拟带噪声的奖励
        noise = np.random.normal(0, 2)
        observed_reward = true_q + noise
        
        # Q学习更新
        q_current += learning_rate * (observed_reward - q_current)
        q_estimates.append(q_current)
    
    plt.plot(q_estimates, 'b-', linewidth=2, label='Q值估计')
    plt.axhline(y=true_q, color='r', linestyle='--', linewidth=2, label='真实Q值')
    plt.xlabel('更新次数')
    plt.ylabel('Q值')
    plt.title('Q值学习收敛过程')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 探索效果对比
    plt.subplot(2, 3, 5)
    # 模拟不同探索策略的学习效果
    episodes_sim = np.arange(100)
    
    # 高探索
    high_exploration = 50 * (1 - np.exp(-episodes_sim / 20)) + np.random.normal(0, 5, len(episodes_sim))
    # 低探索
    low_exploration = 30 * (1 - np.exp(-episodes_sim / 50)) + np.random.normal(0, 2, len(episodes_sim))
    # 平衡探索
    balanced_exploration = 60 * (1 - np.exp(-episodes_sim / 30)) + np.random.normal(0, 3, len(episodes_sim))
    
    plt.plot(episodes_sim, high_exploration, 'r-', linewidth=2, label='高探索 (ε=0.5)')
    plt.plot(episodes_sim, low_exploration, 'b-', linewidth=2, label='低探索 (ε=0.05)')
    plt.plot(episodes_sim, balanced_exploration, 'g-', linewidth=2, label='平衡探索 (衰减)')
    
    plt.xlabel('训练回合')
    plt.ylabel('平均奖励')
    plt.title('不同探索策略的学习效果')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Epsilon对训练的影响
    plt.subplot(2, 3, 6)
    training_phases = ['初期\n(ε=1.0)', '中期\n(ε=0.3)', '后期\n(ε=0.01)']
    exploration_benefits = [0.9, 0.5, 0.1]
    exploitation_benefits = [0.1, 0.7, 0.9]
    
    x = np.arange(len(training_phases))
    width = 0.35
    
    plt.bar(x - width/2, exploration_benefits, width, label='探索收益', color='lightcoral')
    plt.bar(x + width/2, exploitation_benefits, width, label='利用收益', color='lightblue')
    
    plt.xlabel('训练阶段')
    plt.ylabel('相对重要性')
    plt.title('训练不同阶段的探索vs利用')
    plt.xticks(x, training_phases)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("=== Epsilon策略分析 ===")
    print(f"初始Epsilon: {epsilon_exp[0]:.3f}")
    print(f"最终Epsilon: {epsilon_exp[-1]:.3f}")
    print(f"衰减率: 0.995 (每回合保留99.5%)")
    print(f"达到0.1需要回合数: {np.where(np.array(epsilon_exp) <= 0.1)[0][0] if any(np.array(epsilon_exp) <= 0.1) else '未达到'}")

def main():
    """主函数"""
    print("🎯 强化学习核心概念可视化")
    print("=" * 50)
    
    print("\n1. 损失函数对比")
    visualize_loss_function()
    
    print("\n2. 奖励分布分析")
    visualize_reward_distribution()
    
    print("\n3. Epsilon策略详解")
    visualize_epsilon_strategy()
    
    print("\n📚 总结:")
    print("• 损失函数: 衡量Q值预测准确性，MSE适合大多数情况")
    print("• 奖励分布: 稀疏+密集设计，引导智能体学习正确行为")
    print("• Epsilon策略: 平衡探索与利用，从高探索逐渐转向高利用")

if __name__ == "__main__":
    main()
